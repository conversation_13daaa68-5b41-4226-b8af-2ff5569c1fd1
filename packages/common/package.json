{"name": "@rpgjs/common", "version": "4.3.0", "description": "", "main": "lib/index.js", "types": "./lib/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc && npm run build:browser", "watch": "tsc -w", "build:browser": "RPG_TYPE=rpg npx rpgjs build"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "dependencies": {"@rpgjs/tiled": "^4.3.0", "@rpgjs/types": "^4.0.5", "rbush": "^3.0.1", "rxjs": "^7.8.0", "sat": "^0.9.0", "workerpool": "^6.4.0"}, "gitHead": "5abe6ca78be96524d74a052a230f2315c900ddee", "devDependencies": {"@rpgjs/compiler": "^4.3.0", "typescript": "^5.0.4"}, "type": "module"}