# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [4.3.0](https://github.com/RSamaium/RPG-JS/compare/v4.2.2...v4.3.0) (2024-01-29)

**Note:** Version bump only for package @rpgjs/common





## [4.2.1](https://github.com/RSamaium/RPG-JS/compare/v4.2.0...v4.2.1) (2024-01-12)


### Bug Fixes

*  lag after action ([d057e17](https://github.com/RSamaium/RPG-JS/commit/d057e1736475e03a0553882a53b96a4d77e65acf))





# [4.2.0](https://github.com/RSamaium/RPG-JS/compare/v4.1.3...v4.2.0) (2023-12-09)


### Features

* inject function ([902e62f](https://github.com/RSamaium/RPG-JS/commit/902e62ff4fdd9b5bd26ee7d5be9ccae2b051f248))





## [4.1.3](https://github.com/RSamaium/RPG-JS/compare/v4.1.2...v4.1.3) (2023-11-17)

**Note:** Version bump only for package @rpgjs/common





## [4.1.1](https://github.com/RSamaium/RPG-JS/compare/v4.1.0...v4.1.1) (2023-10-27)


### Bug Fixes

* **scene:** click in scene map ([04d56d6](https://github.com/RSamaium/RPG-JS/commit/04d56d6e07c58e2c039732e35ae3b94fc6751fa5))
* **test:** clear after scene tests ([e9a934c](https://github.com/RSamaium/RPG-JS/commit/e9a934c7fc197079036628a94891c371bae4edb2))





# [4.1.0](https://github.com/RSamaium/RPG-JS/compare/v4.0.5...v4.1.0) (2023-10-20)


### Features

* onDetectInShape and onDetectOutShape hooks in event ([b2c6a2f](https://github.com/RSamaium/RPG-JS/commit/b2c6a2f98b3bcc992deb0473a9fb1699874b6e48))





## 4.0.5 (2023-10-18)


### Bug Fixes

* flickering motion of an event as it moves ([b2b8832](https://github.com/RSamaium/RPG-JS/commit/b2b8832a1582933afb64c698f40d1b0e72021780))





## 4.0.4 (2023-10-13)

**Note:** Version bump only for package @rpgjs/common





## 4.0.3 (2023-10-10)

**Note:** Version bump only for package @rpgjs/common





## 4.0.2 (2023-10-03)

**Note:** Version bump only for package @rpgjs/common





## 4.0.1 (2023-10-03)

**Note:** Version bump only for package @rpgjs/common





# 4.0.0-rc.13 (2023-09-09)

**Note:** Version bump only for package @rpgjs/common





# 4.0.0-rc.12 (2023-09-08)

**Note:** Version bump only for package @rpgjs/common





# 4.0.0-rc.11 (2023-08-30)

**Note:** Version bump only for package @rpgjs/common





# 4.0.0-rc.10 (2023-08-28)

**Note:** Version bump only for package @rpgjs/common





# 4.0.0-rc.9 (2023-08-25)

**Note:** Version bump only for package @rpgjs/common





# 4.0.0-rc.8 (2023-08-23)

**Note:** Version bump only for package @rpgjs/common





# [4.0.0-rc.5](https://github.com/RSamaium/RPG-JS/compare/v4.0.0-rc.4...v4.0.0-rc.5) (2023-08-16)

**Note:** Version bump only for package @rpgjs/common
