{"name": "@rpgjs/server", "version": "4.3.0", "description": "", "main": "./lib/index.js", "types": "./lib/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc && npm run build:browser", "build:browser": "RPG_TYPE=rpg npx rpgjs build", "watch": "tsc -w"}, "typesVersions": {"*": {"express": ["lib/express/server.d.ts"]}}, "exports": {".": "./lib/index.js", "./express": "./lib/express/server.js"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "dependencies": {"@rpgjs/common": "^4.3.0", "@rpgjs/database": "^4.3.0", "@rpgjs/tiled": "^4.3.0", "@rpgjs/types": "^4.0.5", "axios": "^1.3.6", "cors": "^2.8.5", "express": "^4.18.2", "lodash.merge": "^4.6.2", "pretty-error": "^4.0.0", "rxjs": "7.8.0", "sat": "^0.9.0", "simple-room": "^3.1.3", "socket.io": "^4.6.1"}, "gitHead": "5abe6ca78be96524d74a052a230f2315c900ddee", "devDependencies": {"@rpgjs/compiler": "^4.3.0", "typescript": "^5.0.4"}, "alias": {"express": "./lib/express/server"}, "type": "module"}