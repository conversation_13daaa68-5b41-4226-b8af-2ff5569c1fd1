{"name": "@rpgjs/fx", "version": "4.3.0", "description": "", "main": "src/index.ts", "types": "index.d.ts", "scripts": {"build": "tsc", "watch": "tsc -w"}, "publishConfig": {"access": "public"}, "files": ["src", "index.d.ts"], "keywords": [], "author": "<PERSON>", "license": "MIT", "dependencies": {"@rpgjs/client": "^4.3.0", "jszip": "3.10.1", "revolt-fx": "1.1.1"}, "gitHead": "5abe6ca78be96524d74a052a230f2315c900ddee"}