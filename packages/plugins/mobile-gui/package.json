{"name": "@rpgjs/mobile-gui", "version": "4.3.0", "description": "", "main": "src/index.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "publishConfig": {"access": "public"}, "files": ["src"], "keywords": [], "author": "<PERSON>", "license": "MIT", "dependencies": {"@rpgjs/client": "^4.3.0", "nipplejs": "^0.10.1", "vue": "^3.2.47"}, "gitHead": "5abe6ca78be96524d74a052a230f2315c900ddee"}