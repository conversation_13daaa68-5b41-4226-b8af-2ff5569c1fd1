import { RpgMap, MapData, RpgPlayer } from '@rpgjs/server'

@MapData({
    id: 'newmap',
    file: require('./tmx/newmap.tmx'),
    name: 'New Adventure Map'
})
export class NewMap extends RpgMap {
    onLoad() {
        console.log('New Adventure Map loaded!')
    }

    on<PERSON><PERSON>n(player: RpgPlayer) {
        console.log(`Player ${player.name || player.id} entered the New Adventure Map`)
        // You can add custom logic here when a player joins the map
        // For example, welcome message, setting player properties, etc.
    }
    
    onLeave(player: RpgPlayer) {
        super.onLeave(player)
        console.log(`Player ${player.name || player.id} left the New Adventure Map`)
        // You can add custom logic here when a player leaves the map
    }
}
