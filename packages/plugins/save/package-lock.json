{"name": "@rpgjs/save", "version": "4.3.1", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "@rpgjs/save", "version": "4.3.1", "license": "MIT", "dependencies": {"@rpgjs/client": "^4.3.0"}}, "../../client": {"name": "@rpgjs/client", "version": "4.0.0", "license": "MIT", "dependencies": {"@pixi/tilemap": "^4.0.0", "@rpgjs/common": "^3.3.2", "@rpgjs/tiled": "^3.3.0", "@rpgjs/types": "^3.3.0", "@types/howler": "2.2.7", "howler": "2.2.3", "lodash.get": "^4.4.2", "lodash.merge": "4.6.2", "pixi-viewport": "5.0.1", "pixi.js": "7.2.4", "rxjs": "7.8.0", "simple-room-client": "^2.0.2", "vue": "^3.2.47"}, "devDependencies": {"@types/css-font-loading-module": "^0.0.8", "@types/node": "^18.16.0", "typescript": "^5.0.4"}}, "../../client/node_modules/@babel/parser": {"version": "7.21.3", "license": "MIT", "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "../../client/node_modules/@pixi/accessibility": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2", "@pixi/events": "7.2.2"}}, "../../client/node_modules/@pixi/app": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2"}}, "../../client/node_modules/@pixi/assets": {"version": "7.2.2", "license": "MIT", "dependencies": {"@types/css-font-loading-module": "^0.0.7"}, "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/utils": "7.2.2"}}, "../../client/node_modules/@pixi/assets/node_modules/@types/css-font-loading-module": {"version": "0.0.7", "license": "MIT"}, "../../client/node_modules/@pixi/color": {"version": "7.2.2", "license": "MIT", "dependencies": {"colord": "^2.9.3"}}, "../../client/node_modules/@pixi/compressed-textures": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/assets": "7.2.2", "@pixi/core": "7.2.2"}}, "../../client/node_modules/@pixi/constants": {"version": "7.2.2", "license": "MIT"}, "../../client/node_modules/@pixi/core": {"version": "7.2.2", "license": "MIT", "dependencies": {"@pixi/color": "7.2.2", "@pixi/constants": "7.2.2", "@pixi/extensions": "7.2.2", "@pixi/math": "7.2.2", "@pixi/runner": "7.2.2", "@pixi/settings": "7.2.2", "@pixi/ticker": "7.2.2", "@pixi/utils": "7.2.2", "@types/offscreencanvas": "^2019.6.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/pixijs"}}, "../../client/node_modules/@pixi/display": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2"}}, "../../client/node_modules/@pixi/events": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2", "@pixi/utils": "7.2.2"}}, "../../client/node_modules/@pixi/extensions": {"version": "7.2.2", "license": "MIT"}, "../../client/node_modules/@pixi/extract": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2"}}, "../../client/node_modules/@pixi/filter-alpha": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2"}}, "../../client/node_modules/@pixi/filter-blur": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2"}}, "../../client/node_modules/@pixi/filter-color-matrix": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2"}}, "../../client/node_modules/@pixi/filter-displacement": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2"}}, "../../client/node_modules/@pixi/filter-fxaa": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2"}}, "../../client/node_modules/@pixi/filter-noise": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2"}}, "../../client/node_modules/@pixi/graphics": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2", "@pixi/sprite": "7.2.2"}}, "../../client/node_modules/@pixi/math": {"version": "7.2.2", "license": "MIT"}, "../../client/node_modules/@pixi/mesh": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2"}}, "../../client/node_modules/@pixi/mesh-extras": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/mesh": "7.2.2"}}, "../../client/node_modules/@pixi/mixin-cache-as-bitmap": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2", "@pixi/sprite": "7.2.2"}}, "../../client/node_modules/@pixi/mixin-get-child-by-name": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/display": "7.2.2"}}, "../../client/node_modules/@pixi/mixin-get-global-position": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2"}}, "../../client/node_modules/@pixi/particle-container": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2", "@pixi/sprite": "7.2.2"}}, "../../client/node_modules/@pixi/prepare": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2", "@pixi/graphics": "7.2.2", "@pixi/text": "7.2.2"}}, "../../client/node_modules/@pixi/runner": {"version": "7.2.2", "license": "MIT"}, "../../client/node_modules/@pixi/settings": {"version": "7.2.2", "license": "MIT", "dependencies": {"@pixi/constants": "7.2.2", "@types/css-font-loading-module": "^0.0.7", "ismobilejs": "^1.1.0"}}, "../../client/node_modules/@pixi/settings/node_modules/@types/css-font-loading-module": {"version": "0.0.7", "license": "MIT"}, "../../client/node_modules/@pixi/sprite": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2"}}, "../../client/node_modules/@pixi/sprite-animated": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/sprite": "7.2.2"}}, "../../client/node_modules/@pixi/sprite-tiling": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2", "@pixi/sprite": "7.2.2"}}, "../../client/node_modules/@pixi/spritesheet": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/assets": "7.2.2", "@pixi/core": "7.2.2"}}, "../../client/node_modules/@pixi/text": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/sprite": "7.2.2"}}, "../../client/node_modules/@pixi/text-bitmap": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/assets": "7.2.2", "@pixi/core": "7.2.2", "@pixi/display": "7.2.2", "@pixi/mesh": "7.2.2", "@pixi/text": "7.2.2"}}, "../../client/node_modules/@pixi/text-html": {"version": "7.2.2", "license": "MIT", "peerDependencies": {"@pixi/core": "7.2.2", "@pixi/display": "7.2.2", "@pixi/sprite": "7.2.2", "@pixi/text": "7.2.2"}}, "../../client/node_modules/@pixi/ticker": {"version": "7.2.2", "license": "MIT", "dependencies": {"@pixi/extensions": "7.2.2", "@pixi/settings": "7.2.2", "@pixi/utils": "7.2.2"}}, "../../client/node_modules/@pixi/tilemap": {"version": "4.0.0", "license": "MIT"}, "../../client/node_modules/@pixi/utils": {"version": "7.2.2", "license": "MIT", "dependencies": {"@pixi/color": "7.2.2", "@pixi/constants": "7.2.2", "@pixi/settings": "7.2.2", "@types/earcut": "^2.1.0", "earcut": "^2.2.4", "eventemitter3": "^4.0.0", "url": "^0.11.0"}}, "../../client/node_modules/@rpgjs/common": {"resolved": "../../common", "link": true}, "../../client/node_modules/@rpgjs/tiled": {"resolved": "../../tiled", "link": true}, "../../client/node_modules/@rpgjs/types": {"resolved": "../../types", "link": true}, "../../client/node_modules/@types/css-font-loading-module": {"version": "0.0.8", "dev": true, "license": "MIT"}, "../../client/node_modules/@types/earcut": {"version": "2.1.1", "license": "MIT"}, "../../client/node_modules/@types/howler": {"version": "2.2.1", "license": "MIT"}, "../../client/node_modules/@types/node": {"version": "18.15.10", "dev": true, "license": "MIT"}, "../../client/node_modules/@types/offscreencanvas": {"version": "2019.7.0", "license": "MIT"}, "../../client/node_modules/@vue/compiler-core": {"version": "3.2.47", "license": "MIT", "dependencies": {"@babel/parser": "^7.16.4", "@vue/shared": "3.2.47", "estree-walker": "^2.0.2", "source-map": "^0.6.1"}}, "../../client/node_modules/@vue/compiler-dom": {"version": "3.2.47", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.2.47", "@vue/shared": "3.2.47"}}, "../../client/node_modules/@vue/compiler-sfc": {"version": "3.2.47", "license": "MIT", "dependencies": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.47", "@vue/compiler-dom": "3.2.47", "@vue/compiler-ssr": "3.2.47", "@vue/reactivity-transform": "3.2.47", "@vue/shared": "3.2.47", "estree-walker": "^2.0.2", "magic-string": "^0.25.7", "postcss": "^8.1.10", "source-map": "^0.6.1"}}, "../../client/node_modules/@vue/compiler-ssr": {"version": "3.2.47", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.2.47", "@vue/shared": "3.2.47"}}, "../../client/node_modules/@vue/reactivity": {"version": "3.2.47", "license": "MIT", "dependencies": {"@vue/shared": "3.2.47"}}, "../../client/node_modules/@vue/reactivity-transform": {"version": "3.2.47", "license": "MIT", "dependencies": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.47", "@vue/shared": "3.2.47", "estree-walker": "^2.0.2", "magic-string": "^0.25.7"}}, "../../client/node_modules/@vue/runtime-core": {"version": "3.2.47", "license": "MIT", "dependencies": {"@vue/reactivity": "3.2.47", "@vue/shared": "3.2.47"}}, "../../client/node_modules/@vue/runtime-dom": {"version": "3.2.47", "license": "MIT", "dependencies": {"@vue/runtime-core": "3.2.47", "@vue/shared": "3.2.47", "csstype": "^2.6.8"}}, "../../client/node_modules/@vue/server-renderer": {"version": "3.2.47", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.2.47", "@vue/shared": "3.2.47"}, "peerDependencies": {"vue": "3.2.47"}}, "../../client/node_modules/@vue/shared": {"version": "3.2.47", "license": "MIT"}, "../../client/node_modules/colord": {"version": "2.9.3", "license": "MIT"}, "../../client/node_modules/csstype": {"version": "2.6.21", "license": "MIT"}, "../../client/node_modules/earcut": {"version": "2.2.4", "license": "ISC"}, "../../client/node_modules/estree-walker": {"version": "2.0.2", "license": "MIT"}, "../../client/node_modules/event-lite": {"version": "0.1.3", "license": "MIT"}, "../../client/node_modules/eventemitter3": {"version": "4.0.7", "license": "MIT"}, "../../client/node_modules/howler": {"version": "2.2.3", "license": "MIT"}, "../../client/node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "../../client/node_modules/int64-buffer": {"version": "0.1.10", "license": "MIT"}, "../../client/node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "../../client/node_modules/ismobilejs": {"version": "1.1.1", "license": "MIT"}, "../../client/node_modules/lodash.get": {"version": "4.4.2", "license": "MIT"}, "../../client/node_modules/lodash.merge": {"version": "4.6.2", "license": "MIT"}, "../../client/node_modules/lodash.mergewith": {"version": "4.6.2", "license": "MIT"}, "../../client/node_modules/magic-string": {"version": "0.25.9", "license": "MIT", "dependencies": {"sourcemap-codec": "^1.4.8"}}, "../../client/node_modules/msgpack-lite": {"version": "0.1.26", "license": "MIT", "dependencies": {"event-lite": "^0.1.1", "ieee754": "^1.1.8", "int64-buffer": "^0.1.9", "isarray": "^1.0.0"}, "bin": {"msgpack": "bin/msgpack"}}, "../../client/node_modules/nanoid": {"version": "3.3.6", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "../../client/node_modules/nanostores": {"version": "0.6.0", "license": "MIT", "engines": {"node": "^14.0.0 || ^16.0.0 || >=18.0.0"}}, "../../client/node_modules/picocolors": {"version": "1.0.0", "license": "ISC"}, "../../client/node_modules/pixi-viewport": {"version": "5.0.1", "license": "MIT"}, "../../client/node_modules/pixi.js": {"version": "7.2.2", "license": "MIT", "dependencies": {"@pixi/accessibility": "7.2.2", "@pixi/app": "7.2.2", "@pixi/assets": "7.2.2", "@pixi/compressed-textures": "7.2.2", "@pixi/core": "7.2.2", "@pixi/display": "7.2.2", "@pixi/events": "7.2.2", "@pixi/extensions": "7.2.2", "@pixi/extract": "7.2.2", "@pixi/filter-alpha": "7.2.2", "@pixi/filter-blur": "7.2.2", "@pixi/filter-color-matrix": "7.2.2", "@pixi/filter-displacement": "7.2.2", "@pixi/filter-fxaa": "7.2.2", "@pixi/filter-noise": "7.2.2", "@pixi/graphics": "7.2.2", "@pixi/mesh": "7.2.2", "@pixi/mesh-extras": "7.2.2", "@pixi/mixin-cache-as-bitmap": "7.2.2", "@pixi/mixin-get-child-by-name": "7.2.2", "@pixi/mixin-get-global-position": "7.2.2", "@pixi/particle-container": "7.2.2", "@pixi/prepare": "7.2.2", "@pixi/sprite": "7.2.2", "@pixi/sprite-animated": "7.2.2", "@pixi/sprite-tiling": "7.2.2", "@pixi/spritesheet": "7.2.2", "@pixi/text": "7.2.2", "@pixi/text-bitmap": "7.2.2", "@pixi/text-html": "7.2.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/pixijs"}}, "../../client/node_modules/postcss": {"version": "8.4.21", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "../../client/node_modules/punycode": {"version": "1.3.2", "license": "MIT"}, "../../client/node_modules/querystring": {"version": "0.2.0", "engines": {"node": ">=0.4.x"}}, "../../client/node_modules/rxjs": {"version": "7.8.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "../../client/node_modules/simple-room-client": {"version": "1.0.0", "license": "MIT", "dependencies": {"lodash.merge": "4.6.2", "lodash.mergewith": "4.6.2", "msgpack-lite": "0.1.26", "nanostores": "^0.6.0", "rxjs": "6.6.3"}}, "../../client/node_modules/simple-room-client/node_modules/rxjs": {"version": "6.6.3", "license": "Apache-2.0", "dependencies": {"tslib": "^1.9.0"}, "engines": {"npm": ">=2.0.0"}}, "../../client/node_modules/simple-room-client/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "../../client/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "../../client/node_modules/source-map-js": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "../../client/node_modules/sourcemap-codec": {"version": "1.4.8", "license": "MIT"}, "../../client/node_modules/tslib": {"version": "2.5.0", "license": "0BSD"}, "../../client/node_modules/typescript": {"version": "5.0.2", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=12.20"}}, "../../client/node_modules/url": {"version": "0.11.0", "license": "MIT", "dependencies": {"punycode": "1.3.2", "querystring": "0.2.0"}}, "../../client/node_modules/vue": {"version": "3.2.47", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.2.47", "@vue/compiler-sfc": "3.2.47", "@vue/runtime-dom": "3.2.47", "@vue/server-renderer": "3.2.47", "@vue/shared": "3.2.47"}}, "../../common": {"name": "@rpgjs/common", "version": "3.3.2", "license": "MIT", "dependencies": {"@rpgjs/tiled": "^3.3.0", "@rpgjs/types": "^3.3.0", "rbush": "^3.0.1", "rxjs": "^7.8.0", "sat": "^0.9.0", "workerpool": "^6.4.0"}, "devDependencies": {"typescript": "^5.0.4"}}, "../../common/node_modules/@rpgjs/tiled": {"resolved": "../../tiled", "link": true}, "../../common/node_modules/@rpgjs/types": {"resolved": "../../types", "link": true}, "../../common/node_modules/quickselect": {"version": "2.0.0", "license": "ISC"}, "../../common/node_modules/rbush": {"version": "3.0.1", "license": "MIT", "dependencies": {"quickselect": "^2.0.0"}}, "../../common/node_modules/rxjs": {"version": "7.8.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "../../common/node_modules/sat": {"version": "0.9.0", "license": "MIT"}, "../../common/node_modules/tslib": {"version": "2.5.0", "license": "0BSD"}, "../../common/node_modules/typescript": {"version": "5.0.2", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=12.20"}}, "../../common/node_modules/workerpool": {"version": "6.4.0", "license": "Apache-2.0"}, "../../database": {"name": "@rpgjs/database", "version": "3.3.0", "extraneous": true, "license": "MIT", "devDependencies": {"typescript": "^5.0.2"}}, "../../server": {"name": "@rpgjs/server", "version": "3.3.2", "extraneous": true, "license": "MIT", "dependencies": {"@rpgjs/common": "^3.3.2", "@rpgjs/database": "^3.3.0", "@rpgjs/tiled": "^3.3.0", "@rpgjs/types": "^3.3.0", "axios": "^1.3.6", "express": "^4.18.2", "lodash.merge": "^4.6.2", "pathfinding": "^0.4.18", "pretty-error": "^4.0.0", "rxjs": "7.8.0", "sat": "^0.9.0", "simple-room": "^2.0.2", "socket.io": "^4.6.1"}, "devDependencies": {"typescript": "^5.0.4"}}, "../../tiled": {"name": "@rpgjs/tiled", "version": "3.3.0", "license": "MIT", "dependencies": {"axios": "^1.3.4", "buffer": "^6.0.3", "xml-js": "^1.6.11", "xmlbuilder": "^15.1.1"}, "devDependencies": {"@types/node": "^18.15.10", "typescript": "^5.0.2"}}, "../../tiled/node_modules/@types/node": {"version": "18.15.10", "dev": true, "license": "MIT"}, "../../tiled/node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "../../tiled/node_modules/axios": {"version": "1.3.4", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.0", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "../../tiled/node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "../../tiled/node_modules/buffer": {"version": "6.0.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "../../tiled/node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "../../tiled/node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "../../tiled/node_modules/follow-redirects": {"version": "1.15.2", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "../../tiled/node_modules/form-data": {"version": "4.0.0", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "../../tiled/node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "../../tiled/node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "../../tiled/node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "../../tiled/node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "../../tiled/node_modules/sax": {"version": "1.2.4", "license": "ISC"}, "../../tiled/node_modules/typescript": {"version": "5.0.2", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=12.20"}}, "../../tiled/node_modules/xml-js": {"version": "1.6.11", "license": "MIT", "dependencies": {"sax": "^1.2.4"}, "bin": {"xml-js": "bin/cli.js"}}, "../../tiled/node_modules/xmlbuilder": {"version": "15.1.1", "license": "MIT", "engines": {"node": ">=8.0"}}, "../../types": {"name": "@rpgjs/types", "version": "3.3.0", "license": "MIT", "dependencies": {"rxjs": "^7.5.7"}, "devDependencies": {"typescript": "^5.0.2"}}, "../../types/node_modules/rxjs": {"version": "7.8.0", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "../../types/node_modules/tslib": {"version": "2.5.0", "license": "0BSD"}, "../../types/node_modules/typescript": {"version": "5.0.2", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=12.20"}}, "node_modules/@rpgjs/client": {"resolved": "../../client", "link": true}}, "dependencies": {"@rpgjs/client": {"version": "file:../../client", "requires": {"@pixi/tilemap": "^4.0.0", "@rpgjs/common": "^3.3.2", "@rpgjs/tiled": "^3.3.0", "@rpgjs/types": "^3.3.0", "@types/css-font-loading-module": "^0.0.8", "@types/howler": "2.2.7", "@types/node": "^18.16.0", "howler": "2.2.3", "lodash.get": "^4.4.2", "lodash.merge": "4.6.2", "pixi-viewport": "5.0.1", "pixi.js": "7.2.4", "rxjs": "7.8.0", "simple-room-client": "^2.0.2", "typescript": "^5.0.4", "vue": "^3.2.47"}, "dependencies": {"@babel/parser": {"version": "7.21.3"}, "@pixi/accessibility": {"version": "7.2.2", "requires": {}}, "@pixi/app": {"version": "7.2.2", "requires": {}}, "@pixi/assets": {"version": "7.2.2", "requires": {"@types/css-font-loading-module": "^0.0.7"}, "dependencies": {"@types/css-font-loading-module": {"version": "0.0.7"}}}, "@pixi/color": {"version": "7.2.2", "requires": {"colord": "^2.9.3"}}, "@pixi/compressed-textures": {"version": "7.2.2", "requires": {}}, "@pixi/constants": {"version": "7.2.2"}, "@pixi/core": {"version": "7.2.2", "requires": {"@pixi/color": "7.2.2", "@pixi/constants": "7.2.2", "@pixi/extensions": "7.2.2", "@pixi/math": "7.2.2", "@pixi/runner": "7.2.2", "@pixi/settings": "7.2.2", "@pixi/ticker": "7.2.2", "@pixi/utils": "7.2.2", "@types/offscreencanvas": "^2019.6.4"}}, "@pixi/display": {"version": "7.2.2", "requires": {}}, "@pixi/events": {"version": "7.2.2", "requires": {}}, "@pixi/extensions": {"version": "7.2.2"}, "@pixi/extract": {"version": "7.2.2", "requires": {}}, "@pixi/filter-alpha": {"version": "7.2.2", "requires": {}}, "@pixi/filter-blur": {"version": "7.2.2", "requires": {}}, "@pixi/filter-color-matrix": {"version": "7.2.2", "requires": {}}, "@pixi/filter-displacement": {"version": "7.2.2", "requires": {}}, "@pixi/filter-fxaa": {"version": "7.2.2", "requires": {}}, "@pixi/filter-noise": {"version": "7.2.2", "requires": {}}, "@pixi/graphics": {"version": "7.2.2", "requires": {}}, "@pixi/math": {"version": "7.2.2"}, "@pixi/mesh": {"version": "7.2.2", "requires": {}}, "@pixi/mesh-extras": {"version": "7.2.2", "requires": {}}, "@pixi/mixin-cache-as-bitmap": {"version": "7.2.2", "requires": {}}, "@pixi/mixin-get-child-by-name": {"version": "7.2.2", "requires": {}}, "@pixi/mixin-get-global-position": {"version": "7.2.2", "requires": {}}, "@pixi/particle-container": {"version": "7.2.2", "requires": {}}, "@pixi/prepare": {"version": "7.2.2", "requires": {}}, "@pixi/runner": {"version": "7.2.2"}, "@pixi/settings": {"version": "7.2.2", "requires": {"@pixi/constants": "7.2.2", "@types/css-font-loading-module": "^0.0.7", "ismobilejs": "^1.1.0"}, "dependencies": {"@types/css-font-loading-module": {"version": "0.0.7"}}}, "@pixi/sprite": {"version": "7.2.2", "requires": {}}, "@pixi/sprite-animated": {"version": "7.2.2", "requires": {}}, "@pixi/sprite-tiling": {"version": "7.2.2", "requires": {}}, "@pixi/spritesheet": {"version": "7.2.2", "requires": {}}, "@pixi/text": {"version": "7.2.2", "requires": {}}, "@pixi/text-bitmap": {"version": "7.2.2", "requires": {}}, "@pixi/text-html": {"version": "7.2.2", "requires": {}}, "@pixi/ticker": {"version": "7.2.2", "requires": {"@pixi/extensions": "7.2.2", "@pixi/settings": "7.2.2", "@pixi/utils": "7.2.2"}}, "@pixi/tilemap": {"version": "4.0.0"}, "@pixi/utils": {"version": "7.2.2", "requires": {"@pixi/color": "7.2.2", "@pixi/constants": "7.2.2", "@pixi/settings": "7.2.2", "@types/earcut": "^2.1.0", "earcut": "^2.2.4", "eventemitter3": "^4.0.0", "url": "^0.11.0"}}, "@rpgjs/common": {"version": "file:../../common", "requires": {"@rpgjs/tiled": "^3.3.0", "@rpgjs/types": "^3.3.0", "rbush": "^3.0.1", "rxjs": "^7.8.0", "sat": "^0.9.0", "typescript": "^5.0.4", "workerpool": "^6.4.0"}, "dependencies": {"@rpgjs/tiled": {"version": "file:../../tiled", "requires": {"@types/node": "^18.15.10", "axios": "^1.3.4", "buffer": "^6.0.3", "typescript": "^5.0.2", "xml-js": "^1.6.11", "xmlbuilder": "^15.1.1"}, "dependencies": {"@types/node": {"version": "18.15.10", "dev": true}, "asynckit": {"version": "0.4.0"}, "axios": {"version": "1.3.4", "requires": {"follow-redirects": "^1.15.0", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "base64-js": {"version": "1.5.1"}, "buffer": {"version": "6.0.3", "requires": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "combined-stream": {"version": "1.0.8", "requires": {"delayed-stream": "~1.0.0"}}, "delayed-stream": {"version": "1.0.0"}, "follow-redirects": {"version": "1.15.2"}, "form-data": {"version": "4.0.0", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}}, "ieee754": {"version": "1.2.1"}, "mime-db": {"version": "1.52.0"}, "mime-types": {"version": "2.1.35", "requires": {"mime-db": "1.52.0"}}, "proxy-from-env": {"version": "1.1.0"}, "sax": {"version": "1.2.4"}, "typescript": {"version": "5.0.2", "dev": true}, "xml-js": {"version": "1.6.11", "requires": {"sax": "^1.2.4"}}, "xmlbuilder": {"version": "15.1.1"}}}, "@rpgjs/types": {"version": "file:../../types", "requires": {"rxjs": "^7.5.7", "typescript": "^5.0.2"}, "dependencies": {"rxjs": {"version": "7.8.0", "requires": {"tslib": "^2.1.0"}}, "tslib": {"version": "2.5.0"}, "typescript": {"version": "5.0.2", "dev": true}}}, "quickselect": {"version": "2.0.0"}, "rbush": {"version": "3.0.1", "requires": {"quickselect": "^2.0.0"}}, "rxjs": {"version": "7.8.0", "requires": {"tslib": "^2.1.0"}}, "sat": {"version": "0.9.0"}, "tslib": {"version": "2.5.0"}, "typescript": {"version": "5.0.2", "dev": true}, "workerpool": {"version": "6.4.0"}}}, "@rpgjs/tiled": {"version": "file:../../tiled", "requires": {"@types/node": "^18.15.10", "axios": "^1.3.4", "buffer": "^6.0.3", "typescript": "^5.0.2", "xml-js": "^1.6.11", "xmlbuilder": "^15.1.1"}, "dependencies": {"@types/node": {"version": "18.15.10", "dev": true}, "asynckit": {"version": "0.4.0"}, "axios": {"version": "1.3.4", "requires": {"follow-redirects": "^1.15.0", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "base64-js": {"version": "1.5.1"}, "buffer": {"version": "6.0.3", "requires": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "combined-stream": {"version": "1.0.8", "requires": {"delayed-stream": "~1.0.0"}}, "delayed-stream": {"version": "1.0.0"}, "follow-redirects": {"version": "1.15.2"}, "form-data": {"version": "4.0.0", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}}, "ieee754": {"version": "1.2.1"}, "mime-db": {"version": "1.52.0"}, "mime-types": {"version": "2.1.35", "requires": {"mime-db": "1.52.0"}}, "proxy-from-env": {"version": "1.1.0"}, "sax": {"version": "1.2.4"}, "typescript": {"version": "5.0.2", "dev": true}, "xml-js": {"version": "1.6.11", "requires": {"sax": "^1.2.4"}}, "xmlbuilder": {"version": "15.1.1"}}}, "@rpgjs/types": {"version": "file:../../types", "requires": {"rxjs": "^7.5.7", "typescript": "^5.0.2"}, "dependencies": {"rxjs": {"version": "7.8.0", "requires": {"tslib": "^2.1.0"}}, "tslib": {"version": "2.5.0"}, "typescript": {"version": "5.0.2", "dev": true}}}, "@types/css-font-loading-module": {"version": "0.0.8", "dev": true}, "@types/earcut": {"version": "2.1.1"}, "@types/howler": {"version": "2.2.1"}, "@types/node": {"version": "18.15.10", "dev": true}, "@types/offscreencanvas": {"version": "2019.7.0"}, "@vue/compiler-core": {"version": "3.2.47", "requires": {"@babel/parser": "^7.16.4", "@vue/shared": "3.2.47", "estree-walker": "^2.0.2", "source-map": "^0.6.1"}}, "@vue/compiler-dom": {"version": "3.2.47", "requires": {"@vue/compiler-core": "3.2.47", "@vue/shared": "3.2.47"}}, "@vue/compiler-sfc": {"version": "3.2.47", "requires": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.47", "@vue/compiler-dom": "3.2.47", "@vue/compiler-ssr": "3.2.47", "@vue/reactivity-transform": "3.2.47", "@vue/shared": "3.2.47", "estree-walker": "^2.0.2", "magic-string": "^0.25.7", "postcss": "^8.1.10", "source-map": "^0.6.1"}}, "@vue/compiler-ssr": {"version": "3.2.47", "requires": {"@vue/compiler-dom": "3.2.47", "@vue/shared": "3.2.47"}}, "@vue/reactivity": {"version": "3.2.47", "requires": {"@vue/shared": "3.2.47"}}, "@vue/reactivity-transform": {"version": "3.2.47", "requires": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.47", "@vue/shared": "3.2.47", "estree-walker": "^2.0.2", "magic-string": "^0.25.7"}}, "@vue/runtime-core": {"version": "3.2.47", "requires": {"@vue/reactivity": "3.2.47", "@vue/shared": "3.2.47"}}, "@vue/runtime-dom": {"version": "3.2.47", "requires": {"@vue/runtime-core": "3.2.47", "@vue/shared": "3.2.47", "csstype": "^2.6.8"}}, "@vue/server-renderer": {"version": "3.2.47", "requires": {"@vue/compiler-ssr": "3.2.47", "@vue/shared": "3.2.47"}}, "@vue/shared": {"version": "3.2.47"}, "colord": {"version": "2.9.3"}, "csstype": {"version": "2.6.21"}, "earcut": {"version": "2.2.4"}, "estree-walker": {"version": "2.0.2"}, "event-lite": {"version": "0.1.3"}, "eventemitter3": {"version": "4.0.7"}, "howler": {"version": "2.2.3"}, "ieee754": {"version": "1.2.1"}, "int64-buffer": {"version": "0.1.10"}, "isarray": {"version": "1.0.0"}, "ismobilejs": {"version": "1.1.1"}, "lodash.get": {"version": "4.4.2"}, "lodash.merge": {"version": "4.6.2"}, "lodash.mergewith": {"version": "4.6.2"}, "magic-string": {"version": "0.25.9", "requires": {"sourcemap-codec": "^1.4.8"}}, "msgpack-lite": {"version": "0.1.26", "requires": {"event-lite": "^0.1.1", "ieee754": "^1.1.8", "int64-buffer": "^0.1.9", "isarray": "^1.0.0"}}, "nanoid": {"version": "3.3.6"}, "nanostores": {"version": "0.6.0"}, "picocolors": {"version": "1.0.0"}, "pixi-viewport": {"version": "5.0.1"}, "pixi.js": {"version": "7.2.2", "requires": {"@pixi/accessibility": "7.2.2", "@pixi/app": "7.2.2", "@pixi/assets": "7.2.2", "@pixi/compressed-textures": "7.2.2", "@pixi/core": "7.2.2", "@pixi/display": "7.2.2", "@pixi/events": "7.2.2", "@pixi/extensions": "7.2.2", "@pixi/extract": "7.2.2", "@pixi/filter-alpha": "7.2.2", "@pixi/filter-blur": "7.2.2", "@pixi/filter-color-matrix": "7.2.2", "@pixi/filter-displacement": "7.2.2", "@pixi/filter-fxaa": "7.2.2", "@pixi/filter-noise": "7.2.2", "@pixi/graphics": "7.2.2", "@pixi/mesh": "7.2.2", "@pixi/mesh-extras": "7.2.2", "@pixi/mixin-cache-as-bitmap": "7.2.2", "@pixi/mixin-get-child-by-name": "7.2.2", "@pixi/mixin-get-global-position": "7.2.2", "@pixi/particle-container": "7.2.2", "@pixi/prepare": "7.2.2", "@pixi/sprite": "7.2.2", "@pixi/sprite-animated": "7.2.2", "@pixi/sprite-tiling": "7.2.2", "@pixi/spritesheet": "7.2.2", "@pixi/text": "7.2.2", "@pixi/text-bitmap": "7.2.2", "@pixi/text-html": "7.2.2"}}, "postcss": {"version": "8.4.21", "requires": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}}, "punycode": {"version": "1.3.2"}, "querystring": {"version": "0.2.0"}, "rxjs": {"version": "7.8.0", "requires": {"tslib": "^2.1.0"}}, "simple-room-client": {"version": "1.0.0", "requires": {"lodash.merge": "4.6.2", "lodash.mergewith": "4.6.2", "msgpack-lite": "0.1.26", "nanostores": "^0.6.0", "rxjs": "6.6.3"}, "dependencies": {"rxjs": {"version": "6.6.3", "requires": {"tslib": "^1.9.0"}}, "tslib": {"version": "1.14.1"}}}, "source-map": {"version": "0.6.1"}, "source-map-js": {"version": "1.0.2"}, "sourcemap-codec": {"version": "1.4.8"}, "tslib": {"version": "2.5.0"}, "typescript": {"version": "5.0.2", "dev": true}, "url": {"version": "0.11.0", "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}}, "vue": {"version": "3.2.47", "requires": {"@vue/compiler-dom": "3.2.47", "@vue/compiler-sfc": "3.2.47", "@vue/runtime-dom": "3.2.47", "@vue/server-renderer": "3.2.47", "@vue/shared": "3.2.47"}}}}}}