{"name": "@rpgjs/title-screen", "version": "4.3.1", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "@rpgjs/title-screen", "version": "4.3.1", "license": "MIT", "dependencies": {"axios": "^1.4.0", "body-parser": "^1.20.2", "mongoose": "^7.4.3", "mongoose-bcrypt": "^1.10.1"}}, "node_modules/@aws-crypto/ie11-detection": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/ie11-detection/-/ie11-detection-3.0.0.tgz", "integrity": "sha512-341lBBkiY1DfDNKai/wXM3aujNBkXR7tq1URPQDL9wi3AUbI80NR74uF1TXHMm7po1AcnFk8iu2S2IeU/+/A+Q==", "optional": true, "dependencies": {"tslib": "^1.11.1"}}, "node_modules/@aws-crypto/ie11-detection/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "optional": true}, "node_modules/@aws-crypto/sha256-browser": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-browser/-/sha256-browser-3.0.0.tgz", "integrity": "sha512-8VLmW2B+gjFbU5uMeqtQM6Nj0/F1bro80xQXCW6CQBWgosFWXTx77aeOF5CAIAmbOK64SdMBJdNr6J41yP5mvQ==", "optional": true, "dependencies": {"@aws-crypto/ie11-detection": "^3.0.0", "@aws-crypto/sha256-js": "^3.0.0", "@aws-crypto/supports-web-crypto": "^3.0.0", "@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "@aws-sdk/util-locate-window": "^3.0.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/sha256-browser/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "optional": true}, "node_modules/@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "optional": true, "dependencies": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/sha256-js/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "optional": true}, "node_modules/@aws-crypto/supports-web-crypto": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/supports-web-crypto/-/supports-web-crypto-3.0.0.tgz", "integrity": "sha512-06hBdMwUAb2WFTuGG73LSC0wfPu93xWwo5vL2et9eymgmu3Id5vFAHBbajVWiGhPO37qcsdCap/FqXvJGJWPIg==", "optional": true, "dependencies": {"tslib": "^1.11.1"}}, "node_modules/@aws-crypto/supports-web-crypto/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "optional": true}, "node_modules/@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "optional": true, "dependencies": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/util/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "optional": true}, "node_modules/@aws-sdk/abort-controller": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/abort-controller/-/abort-controller-3.296.0.tgz", "integrity": "sha512-gNUFBlBw6+sEMfDjPVa83iscpQwXBS4uoiZXnfeQ6s6tnaxqQpJDrBBmNvYqDEXNdaAJX4FhayEwkSvtir/f3A==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/client-cognito-identity": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-cognito-identity/-/client-cognito-identity-3.300.0.tgz", "integrity": "sha512-ZpZNCPbyh/mV7uJJg9ZEiWm1kJUdxm8GWK4NUof2ggrz0CZN5mRi+6lnrm5U7delb/b88sg3EXbCks5z95b/3w==", "optional": true, "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/client-sts": "3.300.0", "@aws-sdk/config-resolver": "3.300.0", "@aws-sdk/credential-provider-node": "3.300.0", "@aws-sdk/fetch-http-handler": "3.296.0", "@aws-sdk/hash-node": "3.296.0", "@aws-sdk/invalid-dependency": "3.296.0", "@aws-sdk/middleware-content-length": "3.296.0", "@aws-sdk/middleware-endpoint": "3.299.0", "@aws-sdk/middleware-host-header": "3.296.0", "@aws-sdk/middleware-logger": "3.296.0", "@aws-sdk/middleware-recursion-detection": "3.296.0", "@aws-sdk/middleware-retry": "3.300.0", "@aws-sdk/middleware-serde": "3.296.0", "@aws-sdk/middleware-signing": "3.299.0", "@aws-sdk/middleware-stack": "3.296.0", "@aws-sdk/middleware-user-agent": "3.299.0", "@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/node-http-handler": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/smithy-client": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "@aws-sdk/util-base64": "3.295.0", "@aws-sdk/util-body-length-browser": "3.295.0", "@aws-sdk/util-body-length-node": "3.295.0", "@aws-sdk/util-defaults-mode-browser": "3.296.0", "@aws-sdk/util-defaults-mode-node": "3.300.0", "@aws-sdk/util-endpoints": "3.296.0", "@aws-sdk/util-retry": "3.296.0", "@aws-sdk/util-user-agent-browser": "3.299.0", "@aws-sdk/util-user-agent-node": "3.300.0", "@aws-sdk/util-utf8": "3.295.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/client-sso": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-sso/-/client-sso-3.300.0.tgz", "integrity": "sha512-zWW7xkDeOKUBrvZsNCtXGT2dx8+/EMkUCGuBoxQrxSpjeX36EIE7DEYOSIGsBDFLOPMZfACKQGEgnowSt8OnCA==", "optional": true, "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/config-resolver": "3.300.0", "@aws-sdk/fetch-http-handler": "3.296.0", "@aws-sdk/hash-node": "3.296.0", "@aws-sdk/invalid-dependency": "3.296.0", "@aws-sdk/middleware-content-length": "3.296.0", "@aws-sdk/middleware-endpoint": "3.299.0", "@aws-sdk/middleware-host-header": "3.296.0", "@aws-sdk/middleware-logger": "3.296.0", "@aws-sdk/middleware-recursion-detection": "3.296.0", "@aws-sdk/middleware-retry": "3.300.0", "@aws-sdk/middleware-serde": "3.296.0", "@aws-sdk/middleware-stack": "3.296.0", "@aws-sdk/middleware-user-agent": "3.299.0", "@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/node-http-handler": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/smithy-client": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "@aws-sdk/util-base64": "3.295.0", "@aws-sdk/util-body-length-browser": "3.295.0", "@aws-sdk/util-body-length-node": "3.295.0", "@aws-sdk/util-defaults-mode-browser": "3.296.0", "@aws-sdk/util-defaults-mode-node": "3.300.0", "@aws-sdk/util-endpoints": "3.296.0", "@aws-sdk/util-retry": "3.296.0", "@aws-sdk/util-user-agent-browser": "3.299.0", "@aws-sdk/util-user-agent-node": "3.300.0", "@aws-sdk/util-utf8": "3.295.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/client-sso-oidc": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-sso-oidc/-/client-sso-oidc-3.300.0.tgz", "integrity": "sha512-A7Gqg1A42Lm7nbNptFdoOi8eGqDtVbmil+snt9dXefGMMkU78NvE6RITUryKIqpbZ3tLiyGDgOpbzWds1Lw6WA==", "optional": true, "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/config-resolver": "3.300.0", "@aws-sdk/fetch-http-handler": "3.296.0", "@aws-sdk/hash-node": "3.296.0", "@aws-sdk/invalid-dependency": "3.296.0", "@aws-sdk/middleware-content-length": "3.296.0", "@aws-sdk/middleware-endpoint": "3.299.0", "@aws-sdk/middleware-host-header": "3.296.0", "@aws-sdk/middleware-logger": "3.296.0", "@aws-sdk/middleware-recursion-detection": "3.296.0", "@aws-sdk/middleware-retry": "3.300.0", "@aws-sdk/middleware-serde": "3.296.0", "@aws-sdk/middleware-stack": "3.296.0", "@aws-sdk/middleware-user-agent": "3.299.0", "@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/node-http-handler": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/smithy-client": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "@aws-sdk/util-base64": "3.295.0", "@aws-sdk/util-body-length-browser": "3.295.0", "@aws-sdk/util-body-length-node": "3.295.0", "@aws-sdk/util-defaults-mode-browser": "3.296.0", "@aws-sdk/util-defaults-mode-node": "3.300.0", "@aws-sdk/util-endpoints": "3.296.0", "@aws-sdk/util-retry": "3.296.0", "@aws-sdk/util-user-agent-browser": "3.299.0", "@aws-sdk/util-user-agent-node": "3.300.0", "@aws-sdk/util-utf8": "3.295.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/client-sts": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-sts/-/client-sts-3.300.0.tgz", "integrity": "sha512-RSgN3M1XPYC6/cW5eh/OjQ7cquGt4sqSyP8EwNJSkaAtRDS410aux4Km91p04dcL0LMXb1J5miAlQUfOiT9Y5A==", "optional": true, "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/config-resolver": "3.300.0", "@aws-sdk/credential-provider-node": "3.300.0", "@aws-sdk/fetch-http-handler": "3.296.0", "@aws-sdk/hash-node": "3.296.0", "@aws-sdk/invalid-dependency": "3.296.0", "@aws-sdk/middleware-content-length": "3.296.0", "@aws-sdk/middleware-endpoint": "3.299.0", "@aws-sdk/middleware-host-header": "3.296.0", "@aws-sdk/middleware-logger": "3.296.0", "@aws-sdk/middleware-recursion-detection": "3.296.0", "@aws-sdk/middleware-retry": "3.300.0", "@aws-sdk/middleware-sdk-sts": "3.299.0", "@aws-sdk/middleware-serde": "3.296.0", "@aws-sdk/middleware-signing": "3.299.0", "@aws-sdk/middleware-stack": "3.296.0", "@aws-sdk/middleware-user-agent": "3.299.0", "@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/node-http-handler": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/smithy-client": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "@aws-sdk/util-base64": "3.295.0", "@aws-sdk/util-body-length-browser": "3.295.0", "@aws-sdk/util-body-length-node": "3.295.0", "@aws-sdk/util-defaults-mode-browser": "3.296.0", "@aws-sdk/util-defaults-mode-node": "3.300.0", "@aws-sdk/util-endpoints": "3.296.0", "@aws-sdk/util-retry": "3.296.0", "@aws-sdk/util-user-agent-browser": "3.299.0", "@aws-sdk/util-user-agent-node": "3.300.0", "@aws-sdk/util-utf8": "3.295.0", "fast-xml-parser": "4.1.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/config-resolver": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/config-resolver/-/config-resolver-3.300.0.tgz", "integrity": "sha512-u3YS+XWjoHmH9wh07Lv+HueYZek/wTO8tlGvVzrlACpaS1JrALuCw8UsJUHNDack63xh9v4oMf+7c0kjuqbmtA==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "@aws-sdk/util-config-provider": "3.295.0", "@aws-sdk/util-middleware": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-cognito-identity": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-cognito-identity/-/credential-provider-cognito-identity-3.300.0.tgz", "integrity": "sha512-pMD8XxiPxUAgjKVf9icq4LHIX12uuuAxEXvBp/FgCfEycqDjQGgQy+bqFuehVXRzN5oRs7wJb2k7LbbMJnQLgg==", "optional": true, "dependencies": {"@aws-sdk/client-cognito-identity": "3.300.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-env": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-env/-/credential-provider-env-3.296.0.tgz", "integrity": "sha512-eDWSU3p04gytkkVXnYn05YzrP5SEaj/DQiafd4y+iBl8IFfF3zM6982rs6qFhvpwrHeSbLqHNfKR1HDWVwfG5g==", "optional": true, "dependencies": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-imds": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-imds/-/credential-provider-imds-3.300.0.tgz", "integrity": "sha512-l7ZFGlr4TjhS0FIt3XwuAJYNAbQ4eDsovMMUVYLDPti1NxlbQDH85xAyaDWF9dU1Gulrpfzz9Ei7q4GYFFPHnQ==", "optional": true, "dependencies": {"@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-ini": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.300.0.tgz", "integrity": "sha512-/yYLJh0zBLe0rWM564Q9XjeRGem3jR32vulKsJye5pKs4PN2RrPyDTgVTXCVsjUAtGs5O0/wvBGRb67suNOcMw==", "optional": true, "dependencies": {"@aws-sdk/credential-provider-env": "3.296.0", "@aws-sdk/credential-provider-imds": "3.300.0", "@aws-sdk/credential-provider-process": "3.300.0", "@aws-sdk/credential-provider-sso": "3.300.0", "@aws-sdk/credential-provider-web-identity": "3.296.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-node": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-node/-/credential-provider-node-3.300.0.tgz", "integrity": "sha512-Lkqv/Fcju8bJpdP0hdwj7QNx2COXOvTcaR0JjJl+C7YGGDpA9GCoWvdNiHCemcaYx3N4bmBBiyPuE+GqJq3gmg==", "optional": true, "dependencies": {"@aws-sdk/credential-provider-env": "3.296.0", "@aws-sdk/credential-provider-imds": "3.300.0", "@aws-sdk/credential-provider-ini": "3.300.0", "@aws-sdk/credential-provider-process": "3.300.0", "@aws-sdk/credential-provider-sso": "3.300.0", "@aws-sdk/credential-provider-web-identity": "3.296.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-process": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-process/-/credential-provider-process-3.300.0.tgz", "integrity": "sha512-HGBLXupPU2XTvHmlcbSgH/zLyhQ1joLIBAvKvyxyjQTIeFSDOufDqRBY4CzNzPv0yJlvSi3gAfL36CR9dh2R4w==", "optional": true, "dependencies": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-sso": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.300.0.tgz", "integrity": "sha512-EtKrCEfd7lsImrtd88hrTwtxldnXNlqM57J1uqWBL11Q67NS66jpwwXBnlKGEAq0u0bS94ckrbzjs4CsiH71Jg==", "optional": true, "dependencies": {"@aws-sdk/client-sso": "3.300.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/token-providers": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-web-identity": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.296.0.tgz", "integrity": "sha512-Rl6Ohoekxe+pccA55XXQDW5wApbg3rGWr6FkmPRcg7Ld6Vfe+HL8OtfsFf83/0eoFerevbif+00BdknXWT05LA==", "optional": true, "dependencies": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-providers": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-providers/-/credential-providers-3.300.0.tgz", "integrity": "sha512-EU06snyCz3oQ4cQqfnVBB+PPNozgWcUk8nDf7PfAKx6UJNn6UWJqve9EbuJYpNvrGHKsecw6Pv8k5HMUjIOIVA==", "optional": true, "dependencies": {"@aws-sdk/client-cognito-identity": "3.300.0", "@aws-sdk/client-sso": "3.300.0", "@aws-sdk/client-sts": "3.300.0", "@aws-sdk/credential-provider-cognito-identity": "3.300.0", "@aws-sdk/credential-provider-env": "3.296.0", "@aws-sdk/credential-provider-imds": "3.300.0", "@aws-sdk/credential-provider-ini": "3.300.0", "@aws-sdk/credential-provider-node": "3.300.0", "@aws-sdk/credential-provider-process": "3.300.0", "@aws-sdk/credential-provider-sso": "3.300.0", "@aws-sdk/credential-provider-web-identity": "3.296.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/fetch-http-handler": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/fetch-http-handler/-/fetch-http-handler-3.296.0.tgz", "integrity": "sha512-wHuKQ+PGKQkYGVuIGscbcbbASl8yIVOSC+QTrZQ4PNsMDvQd9ey2npsmxZk1Z2ULaxY+qYtZCmByyGc8k51TtQ==", "optional": true, "dependencies": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/querystring-builder": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/util-base64": "3.295.0", "tslib": "^2.5.0"}}, "node_modules/@aws-sdk/hash-node": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/hash-node/-/hash-node-3.296.0.tgz", "integrity": "sha512-01Sgxm0NE3rtEznLY8vx1bfNsIeM5Sk5SjY9RXqnvCf9EyaKH9x5FMS/DX/SgDdIYi3aXbTwiwScNVCNBzOIQA==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "@aws-sdk/util-buffer-from": "3.295.0", "@aws-sdk/util-utf8": "3.295.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/invalid-dependency": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/invalid-dependency/-/invalid-dependency-3.296.0.tgz", "integrity": "sha512-dmy4fUds0woHGjxwziaSYCLtb/SOfoEeQjW0GFvHj+YGFyY5hJzna4C759Tt8X5obh1evUXlQcH+FL7TS+7tRQ==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "node_modules/@aws-sdk/is-array-buffer": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/is-array-buffer/-/is-array-buffer-3.295.0.tgz", "integrity": "sha512-SCIt10cr5dud7hvwveU4wkLjvkGssJ3GrcbHCds2NwI+JHmpcaaNYLAqi305JAuT29T36U5ssTFDSmrrEOcfag==", "optional": true, "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-content-length": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-content-length/-/middleware-content-length-3.296.0.tgz", "integrity": "sha512-e7lJm3kkC2pWZdIw23gpMUk1GrpRTBRqhdFfVwyduXw6Wo4nBYv8Z5MOYy3/SlpjE1BDCaPBoZ3O19cO3arHxg==", "optional": true, "dependencies": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-endpoint": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-endpoint/-/middleware-endpoint-3.299.0.tgz", "integrity": "sha512-37BGxHem6yKjSC6zG2xPjvjE7APIDIvwkxL+/K1Jz9+T6AZITcs7tx5y6mIfvaHsdPuCKjrl7Wzg/9jgUKuLkw==", "optional": true, "dependencies": {"@aws-sdk/middleware-serde": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "@aws-sdk/util-middleware": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-host-header": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.296.0.tgz", "integrity": "sha512-V47dFtfkX5lXWv9GDp71gZVCRws4fEdQ9QF9BQ/2UMSNrYjQLg6mFe7NibH+IJoNOid2FIwWIl94Eos636VGYQ==", "optional": true, "dependencies": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-logger": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.296.0.tgz", "integrity": "sha512-LzfEEFyBR9LXdWwLdtBrmi1vLdzgdJNntEgzqktVF8LwaCyY+9xIE6TGu/2V+9fJHAwECxjOC1eQbNQdAZ0Tmw==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-recursion-detection": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.296.0.tgz", "integrity": "sha512-UG7TLDPz9ImQG0uVklHTxE9Us7rTImwN+6el6qZCpoTBuGeXgOkfb0/p8izJyFgY/hMUR4cZqs7IdCDUkxQF3w==", "optional": true, "dependencies": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-retry": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-retry/-/middleware-retry-3.300.0.tgz", "integrity": "sha512-c3tj0Uc64mqnsosAjRBQbit0EUOd0OKrqC5eDB3YCJyLWQSlYRBk4ZBBbN2qTfo3ZCDP+tHgWxRduQlV6Knezg==", "optional": true, "dependencies": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/service-error-classification": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/util-middleware": "3.296.0", "@aws-sdk/util-retry": "3.296.0", "tslib": "^2.5.0", "uuid": "^8.3.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-sdk-sts": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-sdk-sts/-/middleware-sdk-sts-3.299.0.tgz", "integrity": "sha512-yE7IiMQpF1FYqLSYOei4AYM9z62ayFfMMyhKE9IFs+TVaag97uz8NaRlr88HDTcBCZ0CMl6UwNJlZytPD4NjCw==", "optional": true, "dependencies": {"@aws-sdk/middleware-signing": "3.299.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-serde": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-serde/-/middleware-serde-3.296.0.tgz", "integrity": "sha512-xk2PpWAAX758oUTGkGBAncpOr7ddIXisjD2Y2r9DDXuE4JMho2x6zcrVSiYsGIQ6MHZ9XNJKBVDiK9PA4iQWGQ==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-signing": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-signing/-/middleware-signing-3.299.0.tgz", "integrity": "sha512-anhrjeNuo0470QodEmzteFMnqABNebL900yhfODySXCMiaoeTBpo8Qd8t4q4O8PizA7FeLYA3l/5tb/udp7qew==", "optional": true, "dependencies": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/signature-v4": "3.299.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/util-middleware": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-stack": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-stack/-/middleware-stack-3.296.0.tgz", "integrity": "sha512-Rgo7/mdk9tt4qa9+pzG3AoGNhuj7NmnF5H+3DoPm75h58BYP8hKbKobdPGgI2rZLPtO3PGgmyw/4K4tQJPIZ8g==", "optional": true, "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-user-agent": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.299.0.tgz", "integrity": "sha512-Brm5UcbRhuVVmmbpDN8/WSJPCHogV64jGXL5upfL+iJ0c5eZ57LXOZ8kz++t3BU1rEkSIXHJanneEmn7Wbd5sA==", "optional": true, "dependencies": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/util-endpoints": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/node-config-provider": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/node-config-provider/-/node-config-provider-3.300.0.tgz", "integrity": "sha512-60XJV+eW1FyyRNT75kAGdqDHLpWWqnZeCrEyufqQ3BXhhbD1l6oHy5W573DccEO84/0gQYlNbKL8hd8+iB59vA==", "optional": true, "dependencies": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/node-http-handler": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/node-http-handler/-/node-http-handler-3.296.0.tgz", "integrity": "sha512-D15jjPqYSNhEq58BwkmIpD3VwqG4bL5acAaNu5wWAI4S4236JlG+nmpi3gEeE25z1KCwtBl7G30fVRgXYJ2CWA==", "optional": true, "dependencies": {"@aws-sdk/abort-controller": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/querystring-builder": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/property-provider": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/property-provider/-/property-provider-3.296.0.tgz", "integrity": "sha512-kjczxE9Od5LoAKQOmxVWISJ9oPG3aCsB+2+NdI+k9EJFDXUUdMcVV3Skei5uHGgKLMsI6CZy8ezZx6YxOSLSew==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/protocol-http": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/protocol-http/-/protocol-http-3.296.0.tgz", "integrity": "sha512-0U1Z/+tpwdRiSToWo1bpdkbTzjbLugTnd02ATjvK4B7zi363SUGlKfoWgV+v7FU/22CIUI1ZIe7XzXvq5rJfjA==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/querystring-builder": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/querystring-builder/-/querystring-builder-3.296.0.tgz", "integrity": "sha512-+ZrZdTRaVI1R1xKQNrTwuiRoPateUaJ/DNw/myJpTPt+ZRg0H7LKBGaJYwL4pl5l/z1UM/E1fOttSfSW7GHxfw==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "@aws-sdk/util-uri-escape": "3.295.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/querystring-parser": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/querystring-parser/-/querystring-parser-3.296.0.tgz", "integrity": "sha512-nLNZKVQfK42euv7101cE5qfg17YCtGcfccx3B5XSAzvyTROR46kwYqbEvYSsWisbZoRhbQc905gB/5E0U5HDIw==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/service-error-classification": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/service-error-classification/-/service-error-classification-3.296.0.tgz", "integrity": "sha512-YIsWSQ38e1+FqXz3CMrkKS0JD8OLlHf6I72PJhbfegePpQQFqi9R8OREjP5V7UR9Z972yruv4i96ROH6SCtmoA==", "optional": true, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/shared-ini-file-loader": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/shared-ini-file-loader/-/shared-ini-file-loader-3.300.0.tgz", "integrity": "sha512-xA+V08AMsb1EcNJ2UF896T4I3f6Q/H56Z3gTwcXyFXsCY3lYkEB2MEdST+x4+20emELkYjtu5SNsGgUCBehR7g==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/signature-v4": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/signature-v4/-/signature-v4-3.299.0.tgz", "integrity": "sha512-3TtP+S3Tu0Q2/EwJLnN+IEok9nRyez79f6vprqXbC9Lex623cqh/OOYSy2oUjFlIgsIOLPum87/1bfcznYW+yQ==", "optional": true, "dependencies": {"@aws-sdk/is-array-buffer": "3.295.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/util-hex-encoding": "3.295.0", "@aws-sdk/util-middleware": "3.296.0", "@aws-sdk/util-uri-escape": "3.295.0", "@aws-sdk/util-utf8": "3.295.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/smithy-client": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/smithy-client/-/smithy-client-3.296.0.tgz", "integrity": "sha512-HEpsLNozGe9XOWouq5A1TFw5KhFodi8tZqYVNEbSpLoRR+EQKf6OCRvKIRkOn7FnnaOasOR1n7S0D51UG6/irw==", "optional": true, "dependencies": {"@aws-sdk/middleware-stack": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/token-providers": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/token-providers/-/token-providers-3.300.0.tgz", "integrity": "sha512-aDFWG6hBrypvL4zooF2oLVkduo0NepfXkLNO6MCwVVdBksRKIAL9YZFL3NPxpQMH1TyLYz4JhCb6Hh6uz1ftEw==", "optional": true, "dependencies": {"@aws-sdk/client-sso-oidc": "3.300.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/types": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/types/-/types-3.296.0.tgz", "integrity": "sha512-s0wIac64rrMEo2ioUxP9IarGiiCGmelCspNcoNTPSjGl25QqjhyfQqTeGgS58qJ4fHoQb07qra39930xp1IzJg==", "optional": true, "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/url-parser": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/url-parser/-/url-parser-3.296.0.tgz", "integrity": "sha512-nBgeGF+ziuDSLz+y8UAl6zL2tXxDwh3wqeXFe9ZcR4YW71BWuh+vEqEsaEMutOrfnJacCrYKTs9TkIOW41cEGg==", "optional": true, "dependencies": {"@aws-sdk/querystring-parser": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "node_modules/@aws-sdk/util-base64": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-base64/-/util-base64-3.295.0.tgz", "integrity": "sha512-z1r40BsBiOTALnzASvLb4qutGwPpL+jH2UKTCV5WJLXZFMzRnpZaRfeZGE8lMJ/i0+jv9H9G1FmVzE8UgB4rhw==", "optional": true, "dependencies": {"@aws-sdk/util-buffer-from": "3.295.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-body-length-browser": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-body-length-browser/-/util-body-length-browser-3.295.0.tgz", "integrity": "sha512-NbG4/RSHV1VueStPRclSo5zRjNUmcDlNAs29sniZF+YaN0+Ad7hEdu/YgJw39shBfUaurz2Wv0pufU3cxE5Tng==", "optional": true, "dependencies": {"tslib": "^2.5.0"}}, "node_modules/@aws-sdk/util-body-length-node": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-body-length-node/-/util-body-length-node-3.295.0.tgz", "integrity": "sha512-dvGf8VBmrT66lM0n6P/h7wnlHS4Atafyivyl8f4TUCMvRdpqryvvrtnX6yYcq3T7VKQmas/2SOlgDvcrhGXaiw==", "optional": true, "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-buffer-from": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-buffer-from/-/util-buffer-from-3.295.0.tgz", "integrity": "sha512-5ezVEITQnrQKn+CU9qfZHgRp2nrrbX0Clmlm9aiNjAEQEPHY33tWl0t6n8h8yU+IpGiNRMWBVC4aSJaE5NA1mA==", "optional": true, "dependencies": {"@aws-sdk/is-array-buffer": "3.295.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-config-provider": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-config-provider/-/util-config-provider-3.295.0.tgz", "integrity": "sha512-/5Dl1aV2yI8YQjqwmg4RTnl/E9NmNsx7HIwBZt+dTcOrM0LMUwczQBFFcLyqCj/qv5y+VsvLoAAA/OiBT7hb3w==", "optional": true, "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-defaults-mode-browser": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-defaults-mode-browser/-/util-defaults-mode-browser-3.296.0.tgz", "integrity": "sha512-R+nzc0PuTMaOG3LV4FoS5W7oMAqqr8G1IyI+A4Q5iem6YDMF157qV5h6wpIt3A8n9YfjyssLsAT/WPfyv/M79w==", "optional": true, "dependencies": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "bowser": "^2.11.0", "tslib": "^2.5.0"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/@aws-sdk/util-defaults-mode-node": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-defaults-mode-node/-/util-defaults-mode-node-3.300.0.tgz", "integrity": "sha512-a8tZsgkMBhnBlADyhDXMglFh6vkX6zXcJ4pnE9D3JrLDL0Fl50/Zk8FbePilEF2Dv7XRIOe4K70OZnNeeELJcg==", "optional": true, "dependencies": {"@aws-sdk/config-resolver": "3.300.0", "@aws-sdk/credential-provider-imds": "3.300.0", "@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/@aws-sdk/util-endpoints": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-endpoints/-/util-endpoints-3.296.0.tgz", "integrity": "sha512-YraGGLJepXM6HCTaqEGTFf8RFRBdJ0C6uG5k0kVhiXmYxBkeupn8J07CVp9jfWqcPYWElAnMGVEZKU1OjRo4HQ==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-hex-encoding": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-hex-encoding/-/util-hex-encoding-3.295.0.tgz", "integrity": "sha512-XJcoVo41kHzhe28PBm/rqt5mdCp8R6abwiW9ug1dA6FOoPUO8kBUxDv6xaOmA2hfRvd2ocFfBXaUCBqUowkGcQ==", "optional": true, "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-locate-window": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-locate-window/-/util-locate-window-3.295.0.tgz", "integrity": "sha512-d/s+zhUx5Kh4l/ecMP/TBjzp1GR/g89Q4nWH6+wH5WgdHsK+LG+vmsk6mVNuP/8wsCofYG4NBqp5Ulbztbm9QA==", "optional": true, "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-middleware": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-middleware/-/util-middleware-3.296.0.tgz", "integrity": "sha512-MNWU+doVuX+mIehEManP6OP+f08T33qQpHoBqKIeKpn3TjZjMHG7ujACTkJiEOHUrnwTov7h0Sm+3OZwk3kh9w==", "optional": true, "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-retry": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-retry/-/util-retry-3.296.0.tgz", "integrity": "sha512-0mh7SqOMjuJ4vE423SzA/AfCLM68jykbfpEBkTmfqkpjkeQSW+UXHAUdXsMmfzIneiq7go5Z548F868C3cZnwQ==", "optional": true, "dependencies": {"@aws-sdk/service-error-classification": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">= 14.0.0"}}, "node_modules/@aws-sdk/util-uri-escape": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-uri-escape/-/util-uri-escape-3.295.0.tgz", "integrity": "sha512-1H5DcyIoXF8XcPBWf7wzHt0l+TW2EoR8Oq4gsVrPTQkHMTVclC2Yn8EF3gc4arwVBzwLulI9LMBE2L8fexGfTQ==", "optional": true, "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-user-agent-browser": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.299.0.tgz", "integrity": "sha512-TRPAemTDzqxCxbpVkXV+Sp9JbEo0JdT/W8qzP/uuOdglZlNXM+SadkOuNFmqr2KG83bJE6lvomGJcJb9vMN4XQ==", "optional": true, "dependencies": {"@aws-sdk/types": "3.296.0", "bowser": "^2.11.0", "tslib": "^2.5.0"}}, "node_modules/@aws-sdk/util-user-agent-node": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.300.0.tgz", "integrity": "sha512-lBx4HxyTxxQiqGcmvOK4p09XC2YxmH6ANQXdXdiT28qM3OJjf5WLyl4FfdH7grDSryTFdF06FRFtJDFSuSWYrw==", "optional": true, "dependencies": {"@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"aws-crt": ">=1.0.0"}, "peerDependenciesMeta": {"aws-crt": {"optional": true}}}, "node_modules/@aws-sdk/util-utf8": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-utf8/-/util-utf8-3.295.0.tgz", "integrity": "sha512-ITN8v3F63ZkA4sdmCtSbS/mhav4F0MEAiXDAUXtMJLNqVtaVcyQST4i9vNmPpIVthAPAtP0QjyF2tq/Di8bxtQ==", "optional": true, "dependencies": {"@aws-sdk/util-buffer-from": "3.295.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-utf8-browser": {"version": "3.259.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-utf8-browser/-/util-utf8-browser-3.259.0.tgz", "integrity": "sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==", "optional": true, "dependencies": {"tslib": "^2.3.1"}}, "node_modules/@types/node": {"version": "18.15.10", "resolved": "https://registry.npmjs.org/@types/node/-/node-18.15.10.tgz", "integrity": "sha512-9avDaQJczATcXgfmMAW3MIWArOO7A+m90vuCFLr8AotWf8igO/mRoYukrk2cqZVtv38tHs33retzHEilM7FpeQ=="}, "node_modules/@types/webidl-conversions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@types/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "integrity": "sha512-xTE1E+YF4aWPJJeUzaZI5DRntlkY3+BCVJi0axFptnjGmAoWxkyREIh/XMrfxVLejwQxMCfDXdICo0VLxThrog=="}, "node_modules/@types/whatwg-url": {"version": "8.2.2", "resolved": "https://registry.npmjs.org/@types/whatwg-url/-/whatwg-url-8.2.2.tgz", "integrity": "sha512-FtQu10RWgn3D9U4aazdwIE2yzphmTJREDqNdODHrbrZmmMqI0vMheC/6NE/J1Yveaj8H+ela+YwWTjq5PGmuhA==", "dependencies": {"@types/node": "*", "@types/webidl-conversions": "*"}}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "node_modules/axios": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/axios/-/axios-1.4.0.tgz", "integrity": "sha512-S4XCWMEmzvo64T9GfvQDOXgYRDJ/wsSZc7Jvdgx5u1sd0JwsuPLqb3SYmusag+edF6ziyMensPVqLTSc1PiSEA==", "dependencies": {"follow-redirects": "^1.15.0", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/bcryptjs": {"version": "2.4.3", "resolved": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.4.3.tgz", "integrity": "sha512-V/Hy/X9Vt7f3BbPJEi8BdVFMByHi+jNXrYkW3huaybV/kQ0KJg0Y6PkEMbn+zeT+i+SiKZ/HMqJGIIt4LZDqNQ=="}, "node_modules/body-parser": {"version": "1.20.2", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.2.tgz", "integrity": "sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.11.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/bowser": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz", "integrity": "sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==", "optional": true}, "node_modules/bson": {"version": "5.4.0", "resolved": "https://registry.npmjs.org/bson/-/bson-5.4.0.tgz", "integrity": "sha512-WRZ5SQI5GfUuKnPTNmAYPiKIof3ORXAF4IRU5UcgmivNIon01rWQlw5RUH954dpu8yGL8T59YShVddIPaU/gFA==", "engines": {"node": ">=14.20.1"}}, "node_modules/buffer": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "integrity": "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==", "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==", "engines": {"node": ">= 0.6"}}, "node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dependencies": {"ms": "2.0.0"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==", "engines": {"node": ">= 0.8"}}, "node_modules/destroy": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow=="}, "node_modules/fast-xml-parser": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-4.1.2.tgz", "integrity": "sha512-CDYeykkle1LiA/uqQyNwYpFbyF6Axec6YapmpUP+/RHWIoR1zKjocdvNaTsxCxZzQ6v9MLXaSYm9Qq0thv0DHg==", "optional": true, "dependencies": {"strnum": "^1.0.5"}, "bin": {"fxparser": "src/cli/cli.js"}, "funding": {"type": "paypal", "url": "https://paypal.me/naturalintelligence"}}, "node_modules/follow-redirects": {"version": "1.15.2", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.2.tgz", "integrity": "sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/function-bind": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "node_modules/get-intrinsic": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.0.tgz", "integrity": "sha512-L049y6nFOuom5wGyRc3/gdTLO94dySVKRACj1RmJZBQXlbTMhtNIgkWkUHq+jYmZvKf14EW1EoJnnjbmoHij0Q==", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-symbols": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "node_modules/ip": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ip/-/ip-2.0.0.tgz", "integrity": "sha512-WKa+XuLG1A1R0UWhl2+1XQSi+fZWMsYKffMZTTYsiZaUD8k2yDAj5atimTUD2TZkyCkNEeYE5NhFZmupOGtjYQ=="}, "node_modules/kareem": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/kareem/-/kareem-2.5.1.tgz", "integrity": "sha512-7jFxRVm+jD+rkq3kY0iZDJfsO2/t4BBPeEb2qKn2lR/9KhuksYk5hxzfRYWMPV8P/x2d0kHD306YyWLzjjH+uA==", "engines": {"node": ">=12.0.0"}}, "node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==", "engines": {"node": ">= 0.6"}}, "node_modules/memory-pager": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/memory-pager/-/memory-pager-1.5.0.tgz", "integrity": "sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==", "optional": true}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mongodb": {"version": "5.7.0", "resolved": "https://registry.npmjs.org/mongodb/-/mongodb-5.7.0.tgz", "integrity": "sha512-zm82Bq33QbqtxDf58fLWBwTjARK3NSvKYjyz997KSy6hpat0prjeX/kxjbPVyZY60XYPDNETaHkHJI2UCzSLuw==", "dependencies": {"bson": "^5.4.0", "mongodb-connection-string-url": "^2.6.0", "socks": "^2.7.1"}, "engines": {"node": ">=14.20.1"}, "optionalDependencies": {"saslprep": "^1.0.3"}, "peerDependencies": {"@aws-sdk/credential-providers": "^3.201.0", "@mongodb-js/zstd": "^1.1.0", "kerberos": "^2.0.1", "mongodb-client-encryption": ">=2.3.0 <3", "snappy": "^7.2.2"}, "peerDependenciesMeta": {"@aws-sdk/credential-providers": {"optional": true}, "@mongodb-js/zstd": {"optional": true}, "kerberos": {"optional": true}, "mongodb-client-encryption": {"optional": true}, "snappy": {"optional": true}}}, "node_modules/mongodb-connection-string-url": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/mongodb-connection-string-url/-/mongodb-connection-string-url-2.6.0.tgz", "integrity": "sha512-WvTZlI9ab0QYtTYnuMLgobULWhokRjtC7db9LtcVfJ+Hsnyr5eo6ZtNAt3Ly24XZScGMelOcGtm7lSn0332tPQ==", "dependencies": {"@types/whatwg-url": "^8.2.1", "whatwg-url": "^11.0.0"}}, "node_modules/mongoose": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/mongoose/-/mongoose-7.4.3.tgz", "integrity": "sha512-eok0lW6mZJHK2vVSWyJb9tUfPMUuRF3h7YC4pU2K2/YSZBlNDUwvKsHgftMOANbokP2Ry+4ylvzAdW4KjkRFjw==", "dependencies": {"bson": "^5.4.0", "kareem": "2.5.1", "mongodb": "5.7.0", "mpath": "0.9.0", "mquery": "5.0.0", "ms": "2.1.3", "sift": "16.0.1"}, "engines": {"node": ">=14.20.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mongoose"}}, "node_modules/mongoose-bcrypt": {"version": "1.10.1", "resolved": "https://registry.npmjs.org/mongoose-bcrypt/-/mongoose-bcrypt-1.10.1.tgz", "integrity": "sha512-esQhpJ4FfMUD6jtAChFNj10RSJTbK/RKTBQIs0VsZ3nrbj3c+Wmq8MgeuqdNeAq3c0C/pbVA3WtYeWp39cGU3w==", "dependencies": {"bcryptjs": "^2.4.3", "mongoose": "^6.5.2", "semver": "^7.3.7"}}, "node_modules/mongoose-bcrypt/node_modules/bson": {"version": "4.7.2", "resolved": "https://registry.npmjs.org/bson/-/bson-4.7.2.tgz", "integrity": "sha512-Ry9wCtIZ5kGqkJoi6aD8KjxFZEx78guTQDnpXWiNthsxzrxAK/i8E6pCHAIZTbaEFWcOCvbecMukfK7XUvyLpQ==", "dependencies": {"buffer": "^5.6.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/mongoose-bcrypt/node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/mongoose-bcrypt/node_modules/debug/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/mongoose-bcrypt/node_modules/mongodb": {"version": "4.14.0", "resolved": "https://registry.npmjs.org/mongodb/-/mongodb-4.14.0.tgz", "integrity": "sha512-coGKkWXIBczZPr284tYKFLg+KbGPPLlSbdgfKAb6QqCFt5bo5VFZ50O3FFzsw4rnkqjwT6D8Qcoo9nshYKM7Mg==", "dependencies": {"bson": "^4.7.0", "mongodb-connection-string-url": "^2.5.4", "socks": "^2.7.1"}, "engines": {"node": ">=12.9.0"}, "optionalDependencies": {"@aws-sdk/credential-providers": "^3.186.0", "saslprep": "^1.0.3"}}, "node_modules/mongoose-bcrypt/node_modules/mongoose": {"version": "6.10.4", "resolved": "https://registry.npmjs.org/mongoose/-/mongoose-6.10.4.tgz", "integrity": "sha512-xCHVVEaOuhZxbthsKYxvHexWafJqWsl03sD7y7uyyt3euLd1sQoDI8DKueeJq9+hrbWkMkAGbGzgFPTIRqenPg==", "dependencies": {"bson": "^4.7.0", "kareem": "2.5.1", "mongodb": "4.14.0", "mpath": "0.9.0", "mquery": "4.0.3", "ms": "2.1.3", "sift": "16.0.1"}, "engines": {"node": ">=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mongoose"}}, "node_modules/mongoose-bcrypt/node_modules/mquery": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/mquery/-/mquery-4.0.3.tgz", "integrity": "sha512-J5heI+P08I6VJ2Ky3+33IpCdAvlYGTSUjwTPxkAr8i8EoduPMBX2OY/wa3IKZIQl7MU4SbFk8ndgSKyB/cl1zA==", "dependencies": {"debug": "4.x"}, "engines": {"node": ">=12.0.0"}}, "node_modules/mongoose-bcrypt/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node_modules/mongoose/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node_modules/mpath": {"version": "0.9.0", "resolved": "https://registry.npmjs.org/mpath/-/mpath-0.9.0.tgz", "integrity": "sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew==", "engines": {"node": ">=4.0.0"}}, "node_modules/mquery": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/mquery/-/mquery-5.0.0.tgz", "integrity": "sha512-iQMncpmEK8R8ncT8HJGsGc9Dsp8xcgYMVSbs5jgnm1lFHTZqMJTUWTDx1LBO8+mK3tPNZWFLBghQEIOULSTHZg==", "dependencies": {"debug": "4.x"}, "engines": {"node": ">=14.0.0"}}, "node_modules/mquery/node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/mquery/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "node_modules/object-inspect": {"version": "1.12.3", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.3.tgz", "integrity": "sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}, "node_modules/punycode": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.0.tgz", "integrity": "sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.11.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz", "integrity": "sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/raw-body": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz", "integrity": "sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "node_modules/saslprep": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/saslprep/-/saslprep-1.0.3.tgz", "integrity": "sha512-/MY/PEMbk2SuY5sScONwhUDsV2p77Znkb/q3nSVstq/yQzYJOH/Azh29p9oJLsl3LnQwSvZDKagDGBsBwSooag==", "optional": true, "dependencies": {"sparse-bitfield": "^3.0.3"}, "engines": {"node": ">=6"}}, "node_modules/semver": {"version": "7.3.8", "resolved": "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz", "integrity": "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="}, "node_modules/side-channel": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz", "integrity": "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==", "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/sift": {"version": "16.0.1", "resolved": "https://registry.npmjs.org/sift/-/sift-16.0.1.tgz", "integrity": "sha512-Wv6BjQ5zbhW7VFefWusVP33T/EM0vYikCaQ2qR8yULbsilAT8/wQaXvuQ3ptGLpoKx+lihJE3y2UTgKDyyNHZQ=="}, "node_modules/smart-buffer": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz", "integrity": "sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks": {"version": "2.7.1", "resolved": "https://registry.npmjs.org/socks/-/socks-2.7.1.tgz", "integrity": "sha512-7maUZy1N7uo6+WVEX6psASxtNlKaNVMlGQKkG/63nEDdLOWNbiUMoLK7X4uYoLhQstau72mLgfEWcXcwsaHbYQ==", "dependencies": {"ip": "^2.0.0", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.13.0", "npm": ">= 3.0.0"}}, "node_modules/sparse-bitfield": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz", "integrity": "sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==", "optional": true, "dependencies": {"memory-pager": "^1.0.2"}}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==", "engines": {"node": ">= 0.8"}}, "node_modules/strnum": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/strnum/-/strnum-1.0.5.tgz", "integrity": "sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==", "optional": true}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==", "engines": {"node": ">=0.6"}}, "node_modules/tr46": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/tr46/-/tr46-3.0.0.tgz", "integrity": "sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==", "dependencies": {"punycode": "^2.1.1"}, "engines": {"node": ">=12"}}, "node_modules/tslib": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "integrity": "sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg==", "optional": true}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==", "engines": {"node": ">= 0.8"}}, "node_modules/uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "optional": true, "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/webidl-conversions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "integrity": "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==", "engines": {"node": ">=12"}}, "node_modules/whatwg-url": {"version": "11.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-11.0.0.tgz", "integrity": "sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==", "dependencies": {"tr46": "^3.0.0", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/yallist": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="}}, "dependencies": {"@aws-crypto/ie11-detection": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/ie11-detection/-/ie11-detection-3.0.0.tgz", "integrity": "sha512-341lBBkiY1DfDNKai/wXM3aujNBkXR7tq1URPQDL9wi3AUbI80NR74uF1TXHMm7po1AcnFk8iu2S2IeU/+/A+Q==", "optional": true, "requires": {"tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "optional": true}}}, "@aws-crypto/sha256-browser": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-browser/-/sha256-browser-3.0.0.tgz", "integrity": "sha512-8VLmW2B+gjFbU5uMeqtQM6Nj0/F1bro80xQXCW6CQBWgosFWXTx77aeOF5CAIAmbOK64SdMBJdNr6J41yP5mvQ==", "optional": true, "requires": {"@aws-crypto/ie11-detection": "^3.0.0", "@aws-crypto/sha256-js": "^3.0.0", "@aws-crypto/supports-web-crypto": "^3.0.0", "@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "@aws-sdk/util-locate-window": "^3.0.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "optional": true}}}, "@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "optional": true, "requires": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "optional": true}}}, "@aws-crypto/supports-web-crypto": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/supports-web-crypto/-/supports-web-crypto-3.0.0.tgz", "integrity": "sha512-06hBdMwUAb2WFTuGG73LSC0wfPu93xWwo5vL2et9eymgmu3Id5vFAHBbajVWiGhPO37qcsdCap/FqXvJGJWPIg==", "optional": true, "requires": {"tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "optional": true}}}, "@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "optional": true, "requires": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "optional": true}}}, "@aws-sdk/abort-controller": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/abort-controller/-/abort-controller-3.296.0.tgz", "integrity": "sha512-gNUFBlBw6+sEMfDjPVa83iscpQwXBS4uoiZXnfeQ6s6tnaxqQpJDrBBmNvYqDEXNdaAJX4FhayEwkSvtir/f3A==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/client-cognito-identity": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-cognito-identity/-/client-cognito-identity-3.300.0.tgz", "integrity": "sha512-ZpZNCPbyh/mV7uJJg9ZEiWm1kJUdxm8GWK4NUof2ggrz0CZN5mRi+6lnrm5U7delb/b88sg3EXbCks5z95b/3w==", "optional": true, "requires": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/client-sts": "3.300.0", "@aws-sdk/config-resolver": "3.300.0", "@aws-sdk/credential-provider-node": "3.300.0", "@aws-sdk/fetch-http-handler": "3.296.0", "@aws-sdk/hash-node": "3.296.0", "@aws-sdk/invalid-dependency": "3.296.0", "@aws-sdk/middleware-content-length": "3.296.0", "@aws-sdk/middleware-endpoint": "3.299.0", "@aws-sdk/middleware-host-header": "3.296.0", "@aws-sdk/middleware-logger": "3.296.0", "@aws-sdk/middleware-recursion-detection": "3.296.0", "@aws-sdk/middleware-retry": "3.300.0", "@aws-sdk/middleware-serde": "3.296.0", "@aws-sdk/middleware-signing": "3.299.0", "@aws-sdk/middleware-stack": "3.296.0", "@aws-sdk/middleware-user-agent": "3.299.0", "@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/node-http-handler": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/smithy-client": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "@aws-sdk/util-base64": "3.295.0", "@aws-sdk/util-body-length-browser": "3.295.0", "@aws-sdk/util-body-length-node": "3.295.0", "@aws-sdk/util-defaults-mode-browser": "3.296.0", "@aws-sdk/util-defaults-mode-node": "3.300.0", "@aws-sdk/util-endpoints": "3.296.0", "@aws-sdk/util-retry": "3.296.0", "@aws-sdk/util-user-agent-browser": "3.299.0", "@aws-sdk/util-user-agent-node": "3.300.0", "@aws-sdk/util-utf8": "3.295.0", "tslib": "^2.5.0"}}, "@aws-sdk/client-sso": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-sso/-/client-sso-3.300.0.tgz", "integrity": "sha512-zWW7xkDeOKUBrvZsNCtXGT2dx8+/EMkUCGuBoxQrxSpjeX36EIE7DEYOSIGsBDFLOPMZfACKQGEgnowSt8OnCA==", "optional": true, "requires": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/config-resolver": "3.300.0", "@aws-sdk/fetch-http-handler": "3.296.0", "@aws-sdk/hash-node": "3.296.0", "@aws-sdk/invalid-dependency": "3.296.0", "@aws-sdk/middleware-content-length": "3.296.0", "@aws-sdk/middleware-endpoint": "3.299.0", "@aws-sdk/middleware-host-header": "3.296.0", "@aws-sdk/middleware-logger": "3.296.0", "@aws-sdk/middleware-recursion-detection": "3.296.0", "@aws-sdk/middleware-retry": "3.300.0", "@aws-sdk/middleware-serde": "3.296.0", "@aws-sdk/middleware-stack": "3.296.0", "@aws-sdk/middleware-user-agent": "3.299.0", "@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/node-http-handler": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/smithy-client": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "@aws-sdk/util-base64": "3.295.0", "@aws-sdk/util-body-length-browser": "3.295.0", "@aws-sdk/util-body-length-node": "3.295.0", "@aws-sdk/util-defaults-mode-browser": "3.296.0", "@aws-sdk/util-defaults-mode-node": "3.300.0", "@aws-sdk/util-endpoints": "3.296.0", "@aws-sdk/util-retry": "3.296.0", "@aws-sdk/util-user-agent-browser": "3.299.0", "@aws-sdk/util-user-agent-node": "3.300.0", "@aws-sdk/util-utf8": "3.295.0", "tslib": "^2.5.0"}}, "@aws-sdk/client-sso-oidc": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-sso-oidc/-/client-sso-oidc-3.300.0.tgz", "integrity": "sha512-A7Gqg1A42Lm7nbNptFdoOi8eGqDtVbmil+snt9dXefGMMkU78NvE6RITUryKIqpbZ3tLiyGDgOpbzWds1Lw6WA==", "optional": true, "requires": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/config-resolver": "3.300.0", "@aws-sdk/fetch-http-handler": "3.296.0", "@aws-sdk/hash-node": "3.296.0", "@aws-sdk/invalid-dependency": "3.296.0", "@aws-sdk/middleware-content-length": "3.296.0", "@aws-sdk/middleware-endpoint": "3.299.0", "@aws-sdk/middleware-host-header": "3.296.0", "@aws-sdk/middleware-logger": "3.296.0", "@aws-sdk/middleware-recursion-detection": "3.296.0", "@aws-sdk/middleware-retry": "3.300.0", "@aws-sdk/middleware-serde": "3.296.0", "@aws-sdk/middleware-stack": "3.296.0", "@aws-sdk/middleware-user-agent": "3.299.0", "@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/node-http-handler": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/smithy-client": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "@aws-sdk/util-base64": "3.295.0", "@aws-sdk/util-body-length-browser": "3.295.0", "@aws-sdk/util-body-length-node": "3.295.0", "@aws-sdk/util-defaults-mode-browser": "3.296.0", "@aws-sdk/util-defaults-mode-node": "3.300.0", "@aws-sdk/util-endpoints": "3.296.0", "@aws-sdk/util-retry": "3.296.0", "@aws-sdk/util-user-agent-browser": "3.299.0", "@aws-sdk/util-user-agent-node": "3.300.0", "@aws-sdk/util-utf8": "3.295.0", "tslib": "^2.5.0"}}, "@aws-sdk/client-sts": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-sts/-/client-sts-3.300.0.tgz", "integrity": "sha512-RSgN3M1XPYC6/cW5eh/OjQ7cquGt4sqSyP8EwNJSkaAtRDS410aux4Km91p04dcL0LMXb1J5miAlQUfOiT9Y5A==", "optional": true, "requires": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/config-resolver": "3.300.0", "@aws-sdk/credential-provider-node": "3.300.0", "@aws-sdk/fetch-http-handler": "3.296.0", "@aws-sdk/hash-node": "3.296.0", "@aws-sdk/invalid-dependency": "3.296.0", "@aws-sdk/middleware-content-length": "3.296.0", "@aws-sdk/middleware-endpoint": "3.299.0", "@aws-sdk/middleware-host-header": "3.296.0", "@aws-sdk/middleware-logger": "3.296.0", "@aws-sdk/middleware-recursion-detection": "3.296.0", "@aws-sdk/middleware-retry": "3.300.0", "@aws-sdk/middleware-sdk-sts": "3.299.0", "@aws-sdk/middleware-serde": "3.296.0", "@aws-sdk/middleware-signing": "3.299.0", "@aws-sdk/middleware-stack": "3.296.0", "@aws-sdk/middleware-user-agent": "3.299.0", "@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/node-http-handler": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/smithy-client": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "@aws-sdk/util-base64": "3.295.0", "@aws-sdk/util-body-length-browser": "3.295.0", "@aws-sdk/util-body-length-node": "3.295.0", "@aws-sdk/util-defaults-mode-browser": "3.296.0", "@aws-sdk/util-defaults-mode-node": "3.300.0", "@aws-sdk/util-endpoints": "3.296.0", "@aws-sdk/util-retry": "3.296.0", "@aws-sdk/util-user-agent-browser": "3.299.0", "@aws-sdk/util-user-agent-node": "3.300.0", "@aws-sdk/util-utf8": "3.295.0", "fast-xml-parser": "4.1.2", "tslib": "^2.5.0"}}, "@aws-sdk/config-resolver": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/config-resolver/-/config-resolver-3.300.0.tgz", "integrity": "sha512-u3YS+XWjoHmH9wh07Lv+HueYZek/wTO8tlGvVzrlACpaS1JrALuCw8UsJUHNDack63xh9v4oMf+7c0kjuqbmtA==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "@aws-sdk/util-config-provider": "3.295.0", "@aws-sdk/util-middleware": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/credential-provider-cognito-identity": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-cognito-identity/-/credential-provider-cognito-identity-3.300.0.tgz", "integrity": "sha512-pMD8XxiPxUAgjKVf9icq4LHIX12uuuAxEXvBp/FgCfEycqDjQGgQy+bqFuehVXRzN5oRs7wJb2k7LbbMJnQLgg==", "optional": true, "requires": {"@aws-sdk/client-cognito-identity": "3.300.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/credential-provider-env": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-env/-/credential-provider-env-3.296.0.tgz", "integrity": "sha512-eDWSU3p04gytkkVXnYn05YzrP5SEaj/DQiafd4y+iBl8IFfF3zM6982rs6qFhvpwrHeSbLqHNfKR1HDWVwfG5g==", "optional": true, "requires": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/credential-provider-imds": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-imds/-/credential-provider-imds-3.300.0.tgz", "integrity": "sha512-l7ZFGlr4TjhS0FIt3XwuAJYNAbQ4eDsovMMUVYLDPti1NxlbQDH85xAyaDWF9dU1Gulrpfzz9Ei7q4GYFFPHnQ==", "optional": true, "requires": {"@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/credential-provider-ini": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.300.0.tgz", "integrity": "sha512-/yYLJh0zBLe0rWM564Q9XjeRGem3jR32vulKsJye5pKs4PN2RrPyDTgVTXCVsjUAtGs5O0/wvBGRb67suNOcMw==", "optional": true, "requires": {"@aws-sdk/credential-provider-env": "3.296.0", "@aws-sdk/credential-provider-imds": "3.300.0", "@aws-sdk/credential-provider-process": "3.300.0", "@aws-sdk/credential-provider-sso": "3.300.0", "@aws-sdk/credential-provider-web-identity": "3.296.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/credential-provider-node": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-node/-/credential-provider-node-3.300.0.tgz", "integrity": "sha512-Lkqv/Fcju8bJpdP0hdwj7QNx2COXOvTcaR0JjJl+C7YGGDpA9GCoWvdNiHCemcaYx3N4bmBBiyPuE+GqJq3gmg==", "optional": true, "requires": {"@aws-sdk/credential-provider-env": "3.296.0", "@aws-sdk/credential-provider-imds": "3.300.0", "@aws-sdk/credential-provider-ini": "3.300.0", "@aws-sdk/credential-provider-process": "3.300.0", "@aws-sdk/credential-provider-sso": "3.300.0", "@aws-sdk/credential-provider-web-identity": "3.296.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/credential-provider-process": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-process/-/credential-provider-process-3.300.0.tgz", "integrity": "sha512-HGBLXupPU2XTvHmlcbSgH/zLyhQ1joLIBAvKvyxyjQTIeFSDOufDqRBY4CzNzPv0yJlvSi3gAfL36CR9dh2R4w==", "optional": true, "requires": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/credential-provider-sso": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.300.0.tgz", "integrity": "sha512-EtKrCEfd7lsImrtd88hrTwtxldnXNlqM57J1uqWBL11Q67NS66jpwwXBnlKGEAq0u0bS94ckrbzjs4CsiH71Jg==", "optional": true, "requires": {"@aws-sdk/client-sso": "3.300.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/token-providers": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/credential-provider-web-identity": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.296.0.tgz", "integrity": "sha512-Rl6Ohoekxe+pccA55XXQDW5wApbg3rGWr6FkmPRcg7Ld6Vfe+HL8OtfsFf83/0eoFerevbif+00BdknXWT05LA==", "optional": true, "requires": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/credential-providers": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-providers/-/credential-providers-3.300.0.tgz", "integrity": "sha512-EU06snyCz3oQ4cQqfnVBB+PPNozgWcUk8nDf7PfAKx6UJNn6UWJqve9EbuJYpNvrGHKsecw6Pv8k5HMUjIOIVA==", "optional": true, "requires": {"@aws-sdk/client-cognito-identity": "3.300.0", "@aws-sdk/client-sso": "3.300.0", "@aws-sdk/client-sts": "3.300.0", "@aws-sdk/credential-provider-cognito-identity": "3.300.0", "@aws-sdk/credential-provider-env": "3.296.0", "@aws-sdk/credential-provider-imds": "3.300.0", "@aws-sdk/credential-provider-ini": "3.300.0", "@aws-sdk/credential-provider-node": "3.300.0", "@aws-sdk/credential-provider-process": "3.300.0", "@aws-sdk/credential-provider-sso": "3.300.0", "@aws-sdk/credential-provider-web-identity": "3.296.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/fetch-http-handler": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/fetch-http-handler/-/fetch-http-handler-3.296.0.tgz", "integrity": "sha512-wHuKQ+PGKQkYGVuIGscbcbbASl8yIVOSC+QTrZQ4PNsMDvQd9ey2npsmxZk1Z2ULaxY+qYtZCmByyGc8k51TtQ==", "optional": true, "requires": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/querystring-builder": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/util-base64": "3.295.0", "tslib": "^2.5.0"}}, "@aws-sdk/hash-node": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/hash-node/-/hash-node-3.296.0.tgz", "integrity": "sha512-01Sgxm0NE3rtEznLY8vx1bfNsIeM5Sk5SjY9RXqnvCf9EyaKH9x5FMS/DX/SgDdIYi3aXbTwiwScNVCNBzOIQA==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "@aws-sdk/util-buffer-from": "3.295.0", "@aws-sdk/util-utf8": "3.295.0", "tslib": "^2.5.0"}}, "@aws-sdk/invalid-dependency": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/invalid-dependency/-/invalid-dependency-3.296.0.tgz", "integrity": "sha512-dmy4fUds0woHGjxwziaSYCLtb/SOfoEeQjW0GFvHj+YGFyY5hJzna4C759Tt8X5obh1evUXlQcH+FL7TS+7tRQ==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/is-array-buffer": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/is-array-buffer/-/is-array-buffer-3.295.0.tgz", "integrity": "sha512-SCIt10cr5dud7hvwveU4wkLjvkGssJ3GrcbHCds2NwI+JHmpcaaNYLAqi305JAuT29T36U5ssTFDSmrrEOcfag==", "optional": true, "requires": {"tslib": "^2.5.0"}}, "@aws-sdk/middleware-content-length": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-content-length/-/middleware-content-length-3.296.0.tgz", "integrity": "sha512-e7lJm3kkC2pWZdIw23gpMUk1GrpRTBRqhdFfVwyduXw6Wo4nBYv8Z5MOYy3/SlpjE1BDCaPBoZ3O19cO3arHxg==", "optional": true, "requires": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/middleware-endpoint": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-endpoint/-/middleware-endpoint-3.299.0.tgz", "integrity": "sha512-37BGxHem6yKjSC6zG2xPjvjE7APIDIvwkxL+/K1Jz9+T6AZITcs7tx5y6mIfvaHsdPuCKjrl7Wzg/9jgUKuLkw==", "optional": true, "requires": {"@aws-sdk/middleware-serde": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/url-parser": "3.296.0", "@aws-sdk/util-middleware": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/middleware-host-header": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.296.0.tgz", "integrity": "sha512-V47dFtfkX5lXWv9GDp71gZVCRws4fEdQ9QF9BQ/2UMSNrYjQLg6mFe7NibH+IJoNOid2FIwWIl94Eos636VGYQ==", "optional": true, "requires": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/middleware-logger": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.296.0.tgz", "integrity": "sha512-LzfEEFyBR9LXdWwLdtBrmi1vLdzgdJNntEgzqktVF8LwaCyY+9xIE6TGu/2V+9fJHAwECxjOC1eQbNQdAZ0Tmw==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/middleware-recursion-detection": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.296.0.tgz", "integrity": "sha512-UG7TLDPz9ImQG0uVklHTxE9Us7rTImwN+6el6qZCpoTBuGeXgOkfb0/p8izJyFgY/hMUR4cZqs7IdCDUkxQF3w==", "optional": true, "requires": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/middleware-retry": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-retry/-/middleware-retry-3.300.0.tgz", "integrity": "sha512-c3tj0Uc64mqnsosAjRBQbit0EUOd0OKrqC5eDB3YCJyLWQSlYRBk4ZBBbN2qTfo3ZCDP+tHgWxRduQlV6Knezg==", "optional": true, "requires": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/service-error-classification": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/util-middleware": "3.296.0", "@aws-sdk/util-retry": "3.296.0", "tslib": "^2.5.0", "uuid": "^8.3.2"}}, "@aws-sdk/middleware-sdk-sts": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-sdk-sts/-/middleware-sdk-sts-3.299.0.tgz", "integrity": "sha512-yE7IiMQpF1FYqLSYOei4AYM9z62ayFfMMyhKE9IFs+TVaag97uz8NaRlr88HDTcBCZ0CMl6UwNJlZytPD4NjCw==", "optional": true, "requires": {"@aws-sdk/middleware-signing": "3.299.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/middleware-serde": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-serde/-/middleware-serde-3.296.0.tgz", "integrity": "sha512-xk2PpWAAX758oUTGkGBAncpOr7ddIXisjD2Y2r9DDXuE4JMho2x6zcrVSiYsGIQ6MHZ9XNJKBVDiK9PA4iQWGQ==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/middleware-signing": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-signing/-/middleware-signing-3.299.0.tgz", "integrity": "sha512-anhrjeNuo0470QodEmzteFMnqABNebL900yhfODySXCMiaoeTBpo8Qd8t4q4O8PizA7FeLYA3l/5tb/udp7qew==", "optional": true, "requires": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/signature-v4": "3.299.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/util-middleware": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/middleware-stack": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-stack/-/middleware-stack-3.296.0.tgz", "integrity": "sha512-Rgo7/mdk9tt4qa9+pzG3AoGNhuj7NmnF5H+3DoPm75h58BYP8hKbKobdPGgI2rZLPtO3PGgmyw/4K4tQJPIZ8g==", "optional": true, "requires": {"tslib": "^2.5.0"}}, "@aws-sdk/middleware-user-agent": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.299.0.tgz", "integrity": "sha512-Brm5UcbRhuVVmmbpDN8/WSJPCHogV64jGXL5upfL+iJ0c5eZ57LXOZ8kz++t3BU1rEkSIXHJanneEmn7Wbd5sA==", "optional": true, "requires": {"@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/util-endpoints": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/node-config-provider": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/node-config-provider/-/node-config-provider-3.300.0.tgz", "integrity": "sha512-60XJV+eW1FyyRNT75kAGdqDHLpWWqnZeCrEyufqQ3BXhhbD1l6oHy5W573DccEO84/0gQYlNbKL8hd8+iB59vA==", "optional": true, "requires": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/node-http-handler": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/node-http-handler/-/node-http-handler-3.296.0.tgz", "integrity": "sha512-D15jjPqYSNhEq58BwkmIpD3VwqG4bL5acAaNu5wWAI4S4236JlG+nmpi3gEeE25z1KCwtBl7G30fVRgXYJ2CWA==", "optional": true, "requires": {"@aws-sdk/abort-controller": "3.296.0", "@aws-sdk/protocol-http": "3.296.0", "@aws-sdk/querystring-builder": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/property-provider": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/property-provider/-/property-provider-3.296.0.tgz", "integrity": "sha512-kjczxE9Od5LoAKQOmxVWISJ9oPG3aCsB+2+NdI+k9EJFDXUUdMcVV3Skei5uHGgKLMsI6CZy8ezZx6YxOSLSew==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/protocol-http": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/protocol-http/-/protocol-http-3.296.0.tgz", "integrity": "sha512-0U1Z/+tpwdRiSToWo1bpdkbTzjbLugTnd02ATjvK4B7zi363SUGlKfoWgV+v7FU/22CIUI1ZIe7XzXvq5rJfjA==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/querystring-builder": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/querystring-builder/-/querystring-builder-3.296.0.tgz", "integrity": "sha512-+ZrZdTRaVI1R1xKQNrTwuiRoPateUaJ/DNw/myJpTPt+ZRg0H7LKBGaJYwL4pl5l/z1UM/E1fOttSfSW7GHxfw==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "@aws-sdk/util-uri-escape": "3.295.0", "tslib": "^2.5.0"}}, "@aws-sdk/querystring-parser": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/querystring-parser/-/querystring-parser-3.296.0.tgz", "integrity": "sha512-nLNZKVQfK42euv7101cE5qfg17YCtGcfccx3B5XSAzvyTROR46kwYqbEvYSsWisbZoRhbQc905gB/5E0U5HDIw==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/service-error-classification": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/service-error-classification/-/service-error-classification-3.296.0.tgz", "integrity": "sha512-YIsWSQ38e1+FqXz3CMrkKS0JD8OLlHf6I72PJhbfegePpQQFqi9R8OREjP5V7UR9Z972yruv4i96ROH6SCtmoA==", "optional": true}, "@aws-sdk/shared-ini-file-loader": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/shared-ini-file-loader/-/shared-ini-file-loader-3.300.0.tgz", "integrity": "sha512-xA+V08AMsb1EcNJ2UF896T4I3f6Q/H56Z3gTwcXyFXsCY3lYkEB2MEdST+x4+20emELkYjtu5SNsGgUCBehR7g==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/signature-v4": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/signature-v4/-/signature-v4-3.299.0.tgz", "integrity": "sha512-3TtP+S3Tu0Q2/EwJLnN+IEok9nRyez79f6vprqXbC9Lex623cqh/OOYSy2oUjFlIgsIOLPum87/1bfcznYW+yQ==", "optional": true, "requires": {"@aws-sdk/is-array-buffer": "3.295.0", "@aws-sdk/types": "3.296.0", "@aws-sdk/util-hex-encoding": "3.295.0", "@aws-sdk/util-middleware": "3.296.0", "@aws-sdk/util-uri-escape": "3.295.0", "@aws-sdk/util-utf8": "3.295.0", "tslib": "^2.5.0"}}, "@aws-sdk/smithy-client": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/smithy-client/-/smithy-client-3.296.0.tgz", "integrity": "sha512-HEpsLNozGe9XOWouq5A1TFw5KhFodi8tZqYVNEbSpLoRR+EQKf6OCRvKIRkOn7FnnaOasOR1n7S0D51UG6/irw==", "optional": true, "requires": {"@aws-sdk/middleware-stack": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/token-providers": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/token-providers/-/token-providers-3.300.0.tgz", "integrity": "sha512-aDFWG6hBrypvL4zooF2oLVkduo0NepfXkLNO6MCwVVdBksRKIAL9YZFL3NPxpQMH1TyLYz4JhCb6Hh6uz1ftEw==", "optional": true, "requires": {"@aws-sdk/client-sso-oidc": "3.300.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/shared-ini-file-loader": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/types": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/types/-/types-3.296.0.tgz", "integrity": "sha512-s0wIac64rrMEo2ioUxP9IarGiiCGmelCspNcoNTPSjGl25QqjhyfQqTeGgS58qJ4fHoQb07qra39930xp1IzJg==", "optional": true, "requires": {"tslib": "^2.5.0"}}, "@aws-sdk/url-parser": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/url-parser/-/url-parser-3.296.0.tgz", "integrity": "sha512-nBgeGF+ziuDSLz+y8UAl6zL2tXxDwh3wqeXFe9ZcR4YW71BWuh+vEqEsaEMutOrfnJacCrYKTs9TkIOW41cEGg==", "optional": true, "requires": {"@aws-sdk/querystring-parser": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/util-base64": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-base64/-/util-base64-3.295.0.tgz", "integrity": "sha512-z1r40BsBiOTALnzASvLb4qutGwPpL+jH2UKTCV5WJLXZFMzRnpZaRfeZGE8lMJ/i0+jv9H9G1FmVzE8UgB4rhw==", "optional": true, "requires": {"@aws-sdk/util-buffer-from": "3.295.0", "tslib": "^2.5.0"}}, "@aws-sdk/util-body-length-browser": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-body-length-browser/-/util-body-length-browser-3.295.0.tgz", "integrity": "sha512-NbG4/RSHV1VueStPRclSo5zRjNUmcDlNAs29sniZF+YaN0+Ad7hEdu/YgJw39shBfUaurz2Wv0pufU3cxE5Tng==", "optional": true, "requires": {"tslib": "^2.5.0"}}, "@aws-sdk/util-body-length-node": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-body-length-node/-/util-body-length-node-3.295.0.tgz", "integrity": "sha512-dvGf8VBmrT66lM0n6P/h7wnlHS4Atafyivyl8f4TUCMvRdpqryvvrtnX6yYcq3T7VKQmas/2SOlgDvcrhGXaiw==", "optional": true, "requires": {"tslib": "^2.5.0"}}, "@aws-sdk/util-buffer-from": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-buffer-from/-/util-buffer-from-3.295.0.tgz", "integrity": "sha512-5ezVEITQnrQKn+CU9qfZHgRp2nrrbX0Clmlm9aiNjAEQEPHY33tWl0t6n8h8yU+IpGiNRMWBVC4aSJaE5NA1mA==", "optional": true, "requires": {"@aws-sdk/is-array-buffer": "3.295.0", "tslib": "^2.5.0"}}, "@aws-sdk/util-config-provider": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-config-provider/-/util-config-provider-3.295.0.tgz", "integrity": "sha512-/5Dl1aV2yI8YQjqwmg4RTnl/E9NmNsx7HIwBZt+dTcOrM0LMUwczQBFFcLyqCj/qv5y+VsvLoAAA/OiBT7hb3w==", "optional": true, "requires": {"tslib": "^2.5.0"}}, "@aws-sdk/util-defaults-mode-browser": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-defaults-mode-browser/-/util-defaults-mode-browser-3.296.0.tgz", "integrity": "sha512-R+nzc0PuTMaOG3LV4FoS5W7oMAqqr8G1IyI+A4Q5iem6YDMF157qV5h6wpIt3A8n9YfjyssLsAT/WPfyv/M79w==", "optional": true, "requires": {"@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "bowser": "^2.11.0", "tslib": "^2.5.0"}}, "@aws-sdk/util-defaults-mode-node": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-defaults-mode-node/-/util-defaults-mode-node-3.300.0.tgz", "integrity": "sha512-a8tZsgkMBhnBlADyhDXMglFh6vkX6zXcJ4pnE9D3JrLDL0Fl50/Zk8FbePilEF2Dv7XRIOe4K70OZnNeeELJcg==", "optional": true, "requires": {"@aws-sdk/config-resolver": "3.300.0", "@aws-sdk/credential-provider-imds": "3.300.0", "@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/property-provider": "3.296.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/util-endpoints": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-endpoints/-/util-endpoints-3.296.0.tgz", "integrity": "sha512-YraGGLJepXM6HCTaqEGTFf8RFRBdJ0C6uG5k0kVhiXmYxBkeupn8J07CVp9jfWqcPYWElAnMGVEZKU1OjRo4HQ==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/util-hex-encoding": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-hex-encoding/-/util-hex-encoding-3.295.0.tgz", "integrity": "sha512-XJcoVo41kHzhe28PBm/rqt5mdCp8R6abwiW9ug1dA6FOoPUO8kBUxDv6xaOmA2hfRvd2ocFfBXaUCBqUowkGcQ==", "optional": true, "requires": {"tslib": "^2.5.0"}}, "@aws-sdk/util-locate-window": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-locate-window/-/util-locate-window-3.295.0.tgz", "integrity": "sha512-d/s+zhUx5Kh4l/ecMP/TBjzp1GR/g89Q4nWH6+wH5WgdHsK+LG+vmsk6mVNuP/8wsCofYG4NBqp5Ulbztbm9QA==", "optional": true, "requires": {"tslib": "^2.5.0"}}, "@aws-sdk/util-middleware": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-middleware/-/util-middleware-3.296.0.tgz", "integrity": "sha512-MNWU+doVuX+mIehEManP6OP+f08T33qQpHoBqKIeKpn3TjZjMHG7ujACTkJiEOHUrnwTov7h0Sm+3OZwk3kh9w==", "optional": true, "requires": {"tslib": "^2.5.0"}}, "@aws-sdk/util-retry": {"version": "3.296.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-retry/-/util-retry-3.296.0.tgz", "integrity": "sha512-0mh7SqOMjuJ4vE423SzA/AfCLM68jykbfpEBkTmfqkpjkeQSW+UXHAUdXsMmfzIneiq7go5Z548F868C3cZnwQ==", "optional": true, "requires": {"@aws-sdk/service-error-classification": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/util-uri-escape": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-uri-escape/-/util-uri-escape-3.295.0.tgz", "integrity": "sha512-1H5DcyIoXF8XcPBWf7wzHt0l+TW2EoR8Oq4gsVrPTQkHMTVclC2Yn8EF3gc4arwVBzwLulI9LMBE2L8fexGfTQ==", "optional": true, "requires": {"tslib": "^2.5.0"}}, "@aws-sdk/util-user-agent-browser": {"version": "3.299.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.299.0.tgz", "integrity": "sha512-TRPAemTDzqxCxbpVkXV+Sp9JbEo0JdT/W8qzP/uuOdglZlNXM+SadkOuNFmqr2KG83bJE6lvomGJcJb9vMN4XQ==", "optional": true, "requires": {"@aws-sdk/types": "3.296.0", "bowser": "^2.11.0", "tslib": "^2.5.0"}}, "@aws-sdk/util-user-agent-node": {"version": "3.300.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.300.0.tgz", "integrity": "sha512-lBx4HxyTxxQiqGcmvOK4p09XC2YxmH6ANQXdXdiT28qM3OJjf5WLyl4FfdH7grDSryTFdF06FRFtJDFSuSWYrw==", "optional": true, "requires": {"@aws-sdk/node-config-provider": "3.300.0", "@aws-sdk/types": "3.296.0", "tslib": "^2.5.0"}}, "@aws-sdk/util-utf8": {"version": "3.295.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-utf8/-/util-utf8-3.295.0.tgz", "integrity": "sha512-ITN8v3F63ZkA4sdmCtSbS/mhav4F0MEAiXDAUXtMJLNqVtaVcyQST4i9vNmPpIVthAPAtP0QjyF2tq/Di8bxtQ==", "optional": true, "requires": {"@aws-sdk/util-buffer-from": "3.295.0", "tslib": "^2.5.0"}}, "@aws-sdk/util-utf8-browser": {"version": "3.259.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-utf8-browser/-/util-utf8-browser-3.259.0.tgz", "integrity": "sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==", "optional": true, "requires": {"tslib": "^2.3.1"}}, "@types/node": {"version": "18.15.10", "resolved": "https://registry.npmjs.org/@types/node/-/node-18.15.10.tgz", "integrity": "sha512-9avDaQJczATcXgfmMAW3MIWArOO7A+m90vuCFLr8AotWf8igO/mRoYukrk2cqZVtv38tHs33retzHEilM7FpeQ=="}, "@types/webidl-conversions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@types/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "integrity": "sha512-xTE1E+YF4aWPJJeUzaZI5DRntlkY3+BCVJi0axFptnjGmAoWxkyREIh/XMrfxVLejwQxMCfDXdICo0VLxThrog=="}, "@types/whatwg-url": {"version": "8.2.2", "resolved": "https://registry.npmjs.org/@types/whatwg-url/-/whatwg-url-8.2.2.tgz", "integrity": "sha512-FtQu10RWgn3D9U4aazdwIE2yzphmTJREDqNdODHrbrZmmMqI0vMheC/6NE/J1Yveaj8H+ela+YwWTjq5PGmuhA==", "requires": {"@types/node": "*", "@types/webidl-conversions": "*"}}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "axios": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/axios/-/axios-1.4.0.tgz", "integrity": "sha512-S4XCWMEmzvo64T9GfvQDOXgYRDJ/wsSZc7Jvdgx5u1sd0JwsuPLqb3SYmusag+edF6ziyMensPVqLTSc1PiSEA==", "requires": {"follow-redirects": "^1.15.0", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="}, "bcryptjs": {"version": "2.4.3", "resolved": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.4.3.tgz", "integrity": "sha512-V/Hy/X9Vt7f3BbPJEi8BdVFMByHi+jNXrYkW3huaybV/kQ0KJg0Y6PkEMbn+zeT+i+SiKZ/HMqJGIIt4LZDqNQ=="}, "body-parser": {"version": "1.20.2", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.2.tgz", "integrity": "sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==", "requires": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.11.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}}, "bowser": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz", "integrity": "sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==", "optional": true}, "bson": {"version": "5.4.0", "resolved": "https://registry.npmjs.org/bson/-/bson-5.4.0.tgz", "integrity": "sha512-WRZ5SQI5GfUuKnPTNmAYPiKIof3ORXAF4IRU5UcgmivNIon01rWQlw5RUH954dpu8yGL8T59YShVddIPaU/gFA=="}, "buffer": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "integrity": "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==", "requires": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "bytes": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg=="}, "call-bind": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "requires": {"delayed-stream": "~1.0.0"}}, "content-type": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA=="}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="}, "depd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw=="}, "destroy": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg=="}, "ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow=="}, "fast-xml-parser": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-4.1.2.tgz", "integrity": "sha512-CDYeykkle1LiA/uqQyNwYpFbyF6Axec6YapmpUP+/RHWIoR1zKjocdvNaTsxCxZzQ6v9MLXaSYm9Qq0thv0DHg==", "optional": true, "requires": {"strnum": "^1.0.5"}}, "follow-redirects": {"version": "1.15.2", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.2.tgz", "integrity": "sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA=="}, "form-data": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz", "integrity": "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}}, "function-bind": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "get-intrinsic": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.0.tgz", "integrity": "sha512-L049y6nFOuom5wGyRc3/gdTLO94dySVKRACj1RmJZBQXlbTMhtNIgkWkUHq+jYmZvKf14EW1EoJnnjbmoHij0Q==", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "has": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "requires": {"function-bind": "^1.1.1"}}, "has-symbols": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="}, "http-errors": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "requires": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}}, "iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ieee754": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="}, "inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "ip": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ip/-/ip-2.0.0.tgz", "integrity": "sha512-WKa+XuLG1A1R0UWhl2+1XQSi+fZWMsYKffMZTTYsiZaUD8k2yDAj5atimTUD2TZkyCkNEeYE5NhFZmupOGtjYQ=="}, "kareem": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/kareem/-/kareem-2.5.1.tgz", "integrity": "sha512-7jFxRVm+jD+rkq3kY0iZDJfsO2/t4BBPeEb2qKn2lR/9KhuksYk5hxzfRYWMPV8P/x2d0kHD306YyWLzjjH+uA=="}, "lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "requires": {"yallist": "^4.0.0"}}, "media-typer": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ=="}, "memory-pager": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/memory-pager/-/memory-pager-1.5.0.tgz", "integrity": "sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==", "optional": true}, "mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="}, "mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "requires": {"mime-db": "1.52.0"}}, "mongodb": {"version": "5.7.0", "resolved": "https://registry.npmjs.org/mongodb/-/mongodb-5.7.0.tgz", "integrity": "sha512-zm82Bq33QbqtxDf58fLWBwTjARK3NSvKYjyz997KSy6hpat0prjeX/kxjbPVyZY60XYPDNETaHkHJI2UCzSLuw==", "requires": {"bson": "^5.4.0", "mongodb-connection-string-url": "^2.6.0", "saslprep": "^1.0.3", "socks": "^2.7.1"}}, "mongodb-connection-string-url": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/mongodb-connection-string-url/-/mongodb-connection-string-url-2.6.0.tgz", "integrity": "sha512-WvTZlI9ab0QYtTYnuMLgobULWhokRjtC7db9LtcVfJ+Hsnyr5eo6ZtNAt3Ly24XZScGMelOcGtm7lSn0332tPQ==", "requires": {"@types/whatwg-url": "^8.2.1", "whatwg-url": "^11.0.0"}}, "mongoose": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/mongoose/-/mongoose-7.4.3.tgz", "integrity": "sha512-eok0lW6mZJHK2vVSWyJb9tUfPMUuRF3h7YC4pU2K2/YSZBlNDUwvKsHgftMOANbokP2Ry+4ylvzAdW4KjkRFjw==", "requires": {"bson": "^5.4.0", "kareem": "2.5.1", "mongodb": "5.7.0", "mpath": "0.9.0", "mquery": "5.0.0", "ms": "2.1.3", "sift": "16.0.1"}, "dependencies": {"ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}}}, "mongoose-bcrypt": {"version": "1.10.1", "resolved": "https://registry.npmjs.org/mongoose-bcrypt/-/mongoose-bcrypt-1.10.1.tgz", "integrity": "sha512-esQhpJ4FfMUD6jtAChFNj10RSJTbK/RKTBQIs0VsZ3nrbj3c+Wmq8MgeuqdNeAq3c0C/pbVA3WtYeWp39cGU3w==", "requires": {"bcryptjs": "^2.4.3", "mongoose": "^6.5.2", "semver": "^7.3.7"}, "dependencies": {"bson": {"version": "4.7.2", "resolved": "https://registry.npmjs.org/bson/-/bson-4.7.2.tgz", "integrity": "sha512-Ry9wCtIZ5kGqkJoi6aD8KjxFZEx78guTQDnpXWiNthsxzrxAK/i8E6pCHAIZTbaEFWcOCvbecMukfK7XUvyLpQ==", "requires": {"buffer": "^5.6.0"}}, "debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "requires": {"ms": "2.1.2"}, "dependencies": {"ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}}}, "mongodb": {"version": "4.14.0", "resolved": "https://registry.npmjs.org/mongodb/-/mongodb-4.14.0.tgz", "integrity": "sha512-coGKkWXIBczZPr284tYKFLg+KbGPPLlSbdgfKAb6QqCFt5bo5VFZ50O3FFzsw4rnkqjwT6D8Qcoo9nshYKM7Mg==", "requires": {"@aws-sdk/credential-providers": "^3.186.0", "bson": "^4.7.0", "mongodb-connection-string-url": "^2.5.4", "saslprep": "^1.0.3", "socks": "^2.7.1"}}, "mongoose": {"version": "6.10.4", "resolved": "https://registry.npmjs.org/mongoose/-/mongoose-6.10.4.tgz", "integrity": "sha512-xCHVVEaOuhZxbthsKYxvHexWafJqWsl03sD7y7uyyt3euLd1sQoDI8DKueeJq9+hrbWkMkAGbGzgFPTIRqenPg==", "requires": {"bson": "^4.7.0", "kareem": "2.5.1", "mongodb": "4.14.0", "mpath": "0.9.0", "mquery": "4.0.3", "ms": "2.1.3", "sift": "16.0.1"}}, "mquery": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/mquery/-/mquery-4.0.3.tgz", "integrity": "sha512-J5heI+P08I6VJ2Ky3+33IpCdAvlYGTSUjwTPxkAr8i8EoduPMBX2OY/wa3IKZIQl7MU4SbFk8ndgSKyB/cl1zA==", "requires": {"debug": "4.x"}}, "ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}}}, "mpath": {"version": "0.9.0", "resolved": "https://registry.npmjs.org/mpath/-/mpath-0.9.0.tgz", "integrity": "sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew=="}, "mquery": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/mquery/-/mquery-5.0.0.tgz", "integrity": "sha512-iQMncpmEK8R8ncT8HJGsGc9Dsp8xcgYMVSbs5jgnm1lFHTZqMJTUWTDx1LBO8+mK3tPNZWFLBghQEIOULSTHZg==", "requires": {"debug": "4.x"}, "dependencies": {"debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "object-inspect": {"version": "1.12.3", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.3.tgz", "integrity": "sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g=="}, "on-finished": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "requires": {"ee-first": "1.1.1"}}, "proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}, "punycode": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.0.tgz", "integrity": "sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA=="}, "qs": {"version": "6.11.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz", "integrity": "sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==", "requires": {"side-channel": "^1.0.4"}}, "raw-body": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz", "integrity": "sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==", "requires": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}}, "safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "saslprep": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/saslprep/-/saslprep-1.0.3.tgz", "integrity": "sha512-/MY/PEMbk2SuY5sScONwhUDsV2p77Znkb/q3nSVstq/yQzYJOH/Azh29p9oJLsl3LnQwSvZDKagDGBsBwSooag==", "optional": true, "requires": {"sparse-bitfield": "^3.0.3"}}, "semver": {"version": "7.3.8", "resolved": "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz", "integrity": "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==", "requires": {"lru-cache": "^6.0.0"}}, "setprototypeof": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="}, "side-channel": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz", "integrity": "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==", "requires": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "sift": {"version": "16.0.1", "resolved": "https://registry.npmjs.org/sift/-/sift-16.0.1.tgz", "integrity": "sha512-Wv6BjQ5zbhW7VFefWusVP33T/EM0vYikCaQ2qR8yULbsilAT8/wQaXvuQ3ptGLpoKx+lihJE3y2UTgKDyyNHZQ=="}, "smart-buffer": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz", "integrity": "sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg=="}, "socks": {"version": "2.7.1", "resolved": "https://registry.npmjs.org/socks/-/socks-2.7.1.tgz", "integrity": "sha512-7maUZy1N7uo6+WVEX6psASxtNlKaNVMlGQKkG/63nEDdLOWNbiUMoLK7X4uYoLhQstau72mLgfEWcXcwsaHbYQ==", "requires": {"ip": "^2.0.0", "smart-buffer": "^4.2.0"}}, "sparse-bitfield": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz", "integrity": "sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==", "optional": true, "requires": {"memory-pager": "^1.0.2"}}, "statuses": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ=="}, "strnum": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/strnum/-/strnum-1.0.5.tgz", "integrity": "sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==", "optional": true}, "toidentifier": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA=="}, "tr46": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/tr46/-/tr46-3.0.0.tgz", "integrity": "sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==", "requires": {"punycode": "^2.1.1"}}, "tslib": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "integrity": "sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg==", "optional": true}, "type-is": {"version": "1.6.18", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ=="}, "uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "optional": true}, "webidl-conversions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "integrity": "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g=="}, "whatwg-url": {"version": "11.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-11.0.0.tgz", "integrity": "sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==", "requires": {"tr46": "^3.0.0", "webidl-conversions": "^7.0.0"}}, "yallist": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="}}}