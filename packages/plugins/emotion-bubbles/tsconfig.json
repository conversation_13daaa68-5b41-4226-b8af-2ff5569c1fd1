{"compilerOptions": {"target": "es2020", "module": "es2020", "outDir": "dist", "strict": true, "sourceMap": true, "strictNullChecks": true, "strictPropertyInitialization": false, "moduleResolution": "node", "esModuleInterop": true, "removeComments": false, "noUnusedParameters": false, "noUnusedLocals": false, "noImplicitThis": false, "noImplicitAny": false, "noImplicitReturns": false, "declaration": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["node"]}, "include": ["src", "index.d.ts", "tests", "node_modules/@rpgjs/compiler/*.d.ts"]}