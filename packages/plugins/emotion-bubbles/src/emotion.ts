export enum EmotionBubble {
    Like = 'like',
    Confusion = 'confusion',
    Question = 'question',
    LikeBreak = 'like-break',
    Exclamation = 'surprise',
    OneDot = 'one-dot',
    TwoDot = 'two-dot',
    ThreeDot = 'three-dot',
    Dollar = 'dollar',
    Stars = 'starts',
    Music = 'music',
    Exclamation2 = 'exclamation',
    Jaded = 'jaded',
    Star = 'star',
    HaHa = 'haha',
    Sad = 'sad',
    <PERSON><PERSON> = 'hungry',
    Idea = 'idea',
    Z = 'z',
    zZ = 'zz',
    Likes = 'likes',
    Empty = 'empty',
    Circle = 'circle',
    Hangry2 = 'hungry2',
    Cross = 'cross',
    Bead = 'bead',
    Beads = 'beads',
    Happy = 'happy',
    Cloud = 'cloud',
    Surprise = 'surprise'
}