{"name": "@rpgjs/agones-matchmaker", "version": "1.0.0-beta.1", "description": "", "main": "index.js", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc", "watch": "tsc -w", "dev": "nodemon dist/server.js"}, "keywords": [], "author": "", "license": "MIT", "dependencies": {"@kubernetes/client-node": "^0.18.1", "cors": "^2.8.5", "express": "^4.18.2"}, "devDependencies": {"@types/node": "^18.15.10", "nodemon": "^2.0.22", "supertest": "^6.3.3", "typescript": "^5.0.2"}, "type": "module"}