{"compilerOptions": {"target": "es2020", "lib": ["dom", "esnext"], "module": "ES2020", "outDir": "./lib", "rootDir": "./src", "strict": true, "sourceMap": true, "strictNullChecks": true, "strictPropertyInitialization": false, "moduleResolution": "node", "esModuleInterop": true, "removeComments": false, "noUnusedParameters": false, "noUnusedLocals": false, "noImplicitThis": false, "noImplicitAny": false, "noImplicitReturns": false, "declaration": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "stripInternal": true}, "include": ["src"]}