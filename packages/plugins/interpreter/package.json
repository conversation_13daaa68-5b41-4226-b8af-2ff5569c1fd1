{"name": "@rpgjs/interpreter", "version": "4.3.0", "description": "", "main": "src/index.ts", "scripts": {"test": "vitest"}, "exports": {".": "./src/index.ts", "./blocks": "./src/blocks/index.ts"}, "publishConfig": {"access": "public"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "dependencies": {"@rpgjs/server": "^4.3.0", "rxjs": "^7.8.1", "zod": "^3.21.4"}, "gitHead": "5abe6ca78be96524d74a052a230f2315c900ddee"}