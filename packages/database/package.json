{"name": "@rpgjs/database", "version": "4.3.0", "description": "", "main": "lib/index.js", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc && npm run build:browser", "build:browser": "RPG_TYPE=rpg npx rpgjs build", "watch": "tsc -w"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "gitHead": "5abe6ca78be96524d74a052a230f2315c900ddee", "devDependencies": {"@rpgjs/compiler": "^4.3.0", "typescript": "^5.0.2"}, "type": "module"}