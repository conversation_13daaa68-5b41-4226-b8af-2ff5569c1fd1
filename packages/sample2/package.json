{"name": "sample2", "version": "4.3.1", "description": "", "main": "index.js", "scripts": {"dev": "rpgjs dev", "build": "rpgjs build"}, "private": true, "keywords": [], "author": "", "license": "MIT", "dependencies": {"@nanostores/react": "^0.7.1", "@rpgjs/chat": "^4.3.0", "@rpgjs/client": "^4.3.0", "@rpgjs/common": "^4.3.0", "@rpgjs/compiler": "^4.3.0", "@rpgjs/database": "^4.3.0", "@rpgjs/default-gui": "^4.3.0", "@rpgjs/gamepad": "^4.3.0", "@rpgjs/mobile-gui": "^4.3.0", "@rpgjs/plugin-emotion-bubbles": "^4.3.0", "@rpgjs/save": "^4.3.1", "@rpgjs/server": "^4.3.0", "@rpgjs/standalone": "^4.3.0", "@rpgjs/title-screen": "^4.3.1", "react": "^18.2.0", "react-dom": "^18.2.0", "rpgjs-helloworld": "^0.0.1", "socket.io-client": "^4.7.2"}, "type": "module"}