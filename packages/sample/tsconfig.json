{"compilerOptions": {"target": "es2020", "module": "esnext", "outDir": "dist", "strict": true, "sourceMap": true, "strictNullChecks": true, "strictPropertyInitialization": false, "moduleResolution": "node", "esModuleInterop": true, "removeComments": false, "noUnusedParameters": false, "noUnusedLocals": false, "noImplicitThis": false, "noImplicitAny": false, "noImplicitReturns": false, "declaration": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["node", "vite/client"], "resolveJsonModule": true}, "include": ["src", "index.d.ts", "node_modules/@rpgjs/compiler/index.d.ts"]}