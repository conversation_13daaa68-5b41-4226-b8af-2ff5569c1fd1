{"name": "@rpgjs/sample3", "version": "4.3.1", "description": "", "main": "src/server.ts", "types": "index.d.ts", "scripts": {"test": "NODE_ENV=test jest", "build": "rpgjs build", "dev": "rpgjs dev", "start": "node dist/server"}, "keywords": [], "author": "<PERSON>", "private": true, "license": "MIT", "dependencies": {"@rpgjs/agones": "^4.3.0", "@rpgjs/chat": "^4.3.0", "@rpgjs/client": "^4.3.0", "@rpgjs/compiler": "^4.3.0", "@rpgjs/database": "^4.3.0", "@rpgjs/default-gui": "^4.3.0", "@rpgjs/fx": "^4.3.0", "@rpgjs/gamepad": "^4.3.0", "@rpgjs/mobile-gui": "^4.3.0", "@rpgjs/plugin-emotion-bubbles": "^4.3.0", "@rpgjs/save": "^4.3.1", "@rpgjs/server": "^4.3.0", "@rpgjs/standalone": "^4.3.0", "@rpgjs/starter-kit": "^4.3.0", "@rpgjs/testing": "^4.3.0", "@rpgjs/title-screen": "^4.3.1", "@rpgjs/types": "^4.0.5", "socket.io-client": "^4.1.2", "vue": "^3.2.47"}, "devDependencies": {"typescript": "^5.0.2"}, "type": "module", "gitHead": "f92f6329c5635baf373b7214248ca37ceb5fdf03"}