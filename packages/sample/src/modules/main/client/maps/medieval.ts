import { Spritesheet } from '@rpgjs/client'

@Spritesheet({
    images: {
        '[A]Dirt_pipo': require('./assets/Dirt_pipo.png'), 
        '[A]Flower_pipo': require('./assets/Flower_pipo.png'),
        '[A]Grass_pipo': require('./assets/Grass_pipo.png'),
        '[A]Wall-Up_pipo': require('./assets/Wall-Up_pipo.png'),
        '[A]WaterFall_pipo': require('./assets/WaterFall_pipo.png'),
        '[A]Water_pipo': require('./assets/Water_pipo.png'),
        '[Base]BaseChip_pipo': require('./assets/BaseChip_pipo.png')
    }
})
export class MedievalTilesets {

}