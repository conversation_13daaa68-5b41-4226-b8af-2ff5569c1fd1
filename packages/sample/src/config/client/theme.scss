@font-face {
    font-family: "lato";
    src: url('@/config/client/fonts/Lato/Lato-Regular.ttf') format("truetype");
}

/*
    Window StyleSheet
*/
$window-background: linear-gradient(148deg, rgba(79,82,136,0.7) 0%, rgba(42,43,73,0.7) 100%);
$window-border: 2.5px solid white;
$window-border-radius: 5px;
$window-arrow-color: white;
$window-font-size: 25px;
$window-font-color: white;
$window-font-family: 'lato';

$cursor-background: #7782ab;
$cursor-border: 1px solid #9db0c6;

$hero-face: url('@/config/client/assets/face.png');

@mixin window-content {
    // add extra style stylesheet
}