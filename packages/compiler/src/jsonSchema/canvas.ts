export default {
    "canvas": {
        "type": "object",
        "properties": {
            "transparent": {
                "type": "boolean"
            },
            "autoDensity": {
                "type": "boolean"
            },
            "antialias": {
                "type": "boolean"
            },
            "resolution": {
                "type": "number"
            },
            "preserveDrawingBuffer": {
                "type": "boolean"
            },
            "backgroundColor": {
                "type": "number"
            }
        }
    },
    "selector": {
        "type": "string"
    },
    "selectorGui": {
        "type": "string"
    },
    "selectorCanvas": {
        "type": "string"
    },
    "standalone": {
        "type": "boolean"
    },
    "drawMap": {
        "type": "boolean"
    },
    "maxFps": {
        "type": "number"
    },
    "serverFps": {
        "type": "number"
    }
}
