{"name": "@rpgjs/compiler", "version": "4.3.0", "description": "", "main": "./lib/index.js", "types": "./lib/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc && npm run build:browser", "watch": "tsc -w", "test": "vitest  --config vitest.config.ts", "build:browser": "vite build", "watch:browser": "vite build -w"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "dependencies": {"@babel/core": "^7.21.4", "@babel/generator": "^7.21.4", "@babel/parser": "^7.21.4", "@babel/traverse": "^7.21.4", "@babel/types": "^7.21.4", "@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@iarna/toml": "^2.2.5", "@vitejs/plugin-react": "^4.1.0", "@vitejs/plugin-vue": "^4.2.1", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "axios": "^1.3.6", "dedent": "^1.5.1", "default-composer": "^0.5.1", "fs-extra": "^11.1.1", "glob": "^10.2.2", "image-size": "^1.0.2", "joi": "^17.9.2", "ora": "^7.0.1", "picocolors": "^1.0.0", "portfinder": "^1.0.32", "rollup-plugin-node-polyfills": "^0.2.1", "sass": "^1.62.1", "vite": "^4.3.2", "vite-node": "^0.30.1", "vite-plugin-pwa": "^0.16.4", "vite-plugin-require-transform": "^1.0.12", "vitest": "^1.2.2", "vitest-webgl-canvas-mock": "^1.1.0", "xml2js": "^0.5.0", "yargs": "^17.7.1"}, "bin": {"rpgjs": "./lib/index.js"}, "devDependencies": {"mock-fs": "^5.2.0", "typescript": "^5.0.4"}, "type": "module", "gitHead": "5abe6ca78be96524d74a052a230f2315c900ddee"}