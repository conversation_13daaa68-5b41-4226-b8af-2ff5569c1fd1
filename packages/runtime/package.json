{"name": "@rpgjs/runtime", "version": "4.3.0", "description": "", "main": "index.js", "scripts": {"build": "RPG_TYPE=rpg npx rpgjs build"}, "keywords": [], "author": "", "license": "MIT", "dependencies": {"@rpgjs/client": "^4.3.0", "@rpgjs/database": "^4.3.0", "@rpgjs/server": "^4.3.0", "@rpgjs/standalone": "^4.3.0"}, "devDependencies": {"@rpgjs/compiler": "^4.3.0"}, "publishConfig": {"access": "public"}}