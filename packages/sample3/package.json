{"name": "sample3", "private": true, "version": "4.3.1", "type": "module", "scripts": {"dev": "rpgjs dev", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@nanostores/react": "^0.7.1", "@rainbow-me/rainbowkit": "^1.3.3", "@rpgjs/client": "^4.3.0", "@rpgjs/compiler": "^4.3.0", "@rpgjs/default-gui": "^4.3.0", "@rpgjs/server": "^4.3.0", "@rpgjs/standalone": "^4.3.0", "@rpgjs/web3": "^4.3.1", "axios": "^1.6.5", "lucide-react": "^0.309.0", "nanostores": "^0.9.5", "react": "^18.2.0", "react-dom": "^18.2.0", "siwe": "^2.1.4", "socket.io-client": "^4.7.3", "viem": "^1.21.4", "wagmi": "^1.4.13"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.0.8"}}