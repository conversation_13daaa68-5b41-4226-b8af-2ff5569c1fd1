# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [4.3.1](https://github.com/RSamaium/RPG-JS/compare/v4.3.0...v4.3.1) (2024-01-29)

**Note:** Version bump only for package sample3





# [4.3.0](https://github.com/RSamaium/RPG-JS/compare/v4.2.2...v4.3.0) (2024-01-29)


### Bug Fixes

* server url env ([e6a1237](https://github.com/RSamaium/RPG-JS/commit/e6a1237e360f7a25e2e8194127473e8a74d0b0ab))
* wallet address ([4f95fa5](https://github.com/RSamaium/RPG-JS/commit/4f95fa551f2dcc1cce23cc14a8dbbef677d25353))


### Features

* loading the game in a React app ([437917a](https://github.com/RSamaium/RPG-JS/commit/437917ace5de1a88777d84c4b39a48e147f77de4))
* reat app integration ([1b9b5d8](https://github.com/RSamaium/RPG-JS/commit/1b9b5d8bb5dd02bcf4a68ccd5eee19c6fc00a4eb))
