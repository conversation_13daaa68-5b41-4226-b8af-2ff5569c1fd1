export enum Control {
    Action = 'action',
    Attack = 'attack',
    Defense = 'defense',
    Skill = 'skill',
    Back = 'back',
    Up = 1,
    Down = 3,
    Right = 2,
    Left = 4
}

export enum Input {
    Break = 'break',
    Backspace = 'backspace',
    Tab = 'tab',
    Clear = 'clear',
    Enter = 'enter',
    Shift = 'shift',
    Ctrl = 'ctrl',
    Alt = 'alt',
    Pause = 'pause/break',
    CapsLock = 'caps lock',
    Escape = 'escape',
    Conversion = 'conversion',
    NonConversion = 'non-conversion',
    Space = 'space',
    PageUp = 'page up',
    PageDown = 'page down',
    End = 'end',
    Home = 'home',
    Left = 4,
    Up = 1,
    Right = 2,
    Down = 3,
    Select = 'select',
    Print = 'print',
    Execute = 'execute',
    PrintScreen = 'Print Screen',
    Insert = 'insert',
    Delete = 'delete',
    Zero = '0',
    One = '1',
    Two = '2',
    Three = '3',
    Four = '4',
    Five = '5',
    Six = '6',
    Seven = '7',
    Height = '8',
    Nine = '9',
    Equal = '=',
    Semicolon = 'semicolon (firefox), equals',
    LessThan = '<',
    Equals = 'equals (firefox)',
    Beta = 'ß',
    At = '@',
    A = 'a',
    B = 'b',
    C = 'c',
    D = 'd',
    E = 'e',
    F = 'f',
    G = 'g',
    H = 'h',
    I = 'i',
    J = 'j',
    K = 'k',
    L = 'l',
    M = 'm',
    N = 'n',
    O = 'o',
    P = 'p',
    Q = 'q',
    R = 'r',
    S = 's',
    T = 't',
    U = 'u',
    V = 'v',
    W = 'w',
    X = 'x',
    Y = 'y',
    Z = 'z',
    SearchKey = 'Windows Key / Left ⌘ / Chromebook Search key',
    NumPad0 = 'numpad 0',
    NumPad1 = 'numpad 1',
    NumPad2 = 'numpad 2',
    NumPad3 = 'numpad 3',
    NumPad4 = 'numpad 4',
    NumPad5 = 'numpad 5',
    NumPad6 = 'numpad 6',
    NumPad7 = 'numpad 7',
    NumPad8 = 'numpad 8',
    NumPad9 = 'numpad 9',
    Multiply = 'multiply',
    Add = 'add',
    Subtract = 'subtract',
    DecimalPoint = 'decimal point',
    Divide = 'divide',
    F1 = 'f1',
    F2 = 'f2',
    F3 = 'f3',
    F4 = 'f4',
    F5 = 'f5',
    F6 = 'f6',
    F7 = 'f7',
    F8 = 'f8',
    F9 = 'f9',
    F10 = 'f10',
    F11 = 'f11',
    F12 = 'f12',
    F13 = 'f13',
    F14 = 'f14',
    F15 = 'f15',
    F16 = 'f16',
    F17 = 'f17',
    F18 = 'f18',
    F19 = 'f19',
    F20 = 'f20',
    F21 = 'f21',
    F22 = 'f22',
    F23 = 'f23',
    F24 = 'f24',
    NumLock = 'num lock',
    ScrollLock = 'scroll lock',
    CircumflexAccent = '^',
    ExclamationMark = '!',
    Hash = '#',
    Dollar = '$',
    AccentU = 'ù',
    PageBackward = 'page backward',
    PageForWard = 'page forward',
    Star = '*',
    DecreaseVolume = 'decrease volume level',
    IncreaseVolume = 'increase volume level',
    Next = 'next',
    Previous = 'previous',
    Stop = 'stop',
    PlayPause = 'play/pause',
    Email = 'e-mail',
    SemiColon = 'semi-colon / ñ',
    EqualSign = 'equal sign',
    Comma = 'comma',
    Dash = 'dash',
    FowardSlach = 'forward slash / ç',
    GraveAccent = 'grave accent / ñ / æ',
    OpenBracket = 'open bracket',
    BackSlach = 'back slash',
    CloseBracket = 'close bracket / å',
    SingleQuote = 'single quote / ø',
    BackQuote = '`',
    Altgr = 'altgr'
}

export interface ControlOptions {
    repeat?: boolean
    bind: string | string[] | Input | Input[]
    method?: Function
    delay?: number | {
        duration: number
        otherControls?: (string | Control)[]
    }
}

export interface Controls {
    [controlName: string]: ControlOptions
}