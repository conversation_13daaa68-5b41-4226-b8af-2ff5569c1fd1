{"name": "@rpgjs/standalone", "version": "4.3.0", "description": "", "main": "src/index.ts", "scripts": {"build:browser": "RPG_TYPE=rpg npx rpgjs build"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "dependencies": {"@rpgjs/client": "^4.3.0", "@rpgjs/common": "^4.3.0", "@rpgjs/server": "^4.3.0", "simple-room": "^3.0.6"}, "devDependencies": {"@rpgjs/compiler": "^4.3.0", "typescript": "^5.0.2"}, "gitHead": "5abe6ca78be96524d74a052a230f2315c900ddee", "type": "module"}