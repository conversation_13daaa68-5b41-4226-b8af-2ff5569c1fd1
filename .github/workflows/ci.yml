name: RPG CI

on:
  push:
    branches:
      - '*'
    paths-ignore:
      - 'docs/**'
  pull_request:
    branches:
      - '*'
    paths-ignore:
      - 'docs/**'

jobs:
  tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x]
    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - run: npm i
    - run: npm test

  build_and_publish:
    needs: tests
    if: github.ref == 'refs/heads/v4'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0
    - name: Use Node.js 18.x
      uses: actions/setup-node@v3
      with:
        node-version: 18.x
        registry-url: 'https://registry.npmjs.org'
        cache: 'npm'
    - run: npm i
    - run: npm run build
    - name: "Version and publish" 
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      run: |
        git config user.name "${{ github.actor }}"
        git config user.email "${{ github.actor}}@users.noreply.github.com"

        npm set //registry.npmjs.org/:_authToken=$NPM_TOKEN

        npx lerna version --conventional-commits --yes
        npx lerna publish from-git --no-private --loglevel=verbose --yes

  starter_repo_update_dependencies:
    needs: build_and_publish
    runs-on: ubuntu-latest
    steps:
    - name: Checkout the starter repository
      run: git clone https://github.com/rpgjs/starter

    - name: Install npm-check-updates globally
      run: npm i npm-check-updates -g

    - name: Run ncu to update dependencies
      run: |
        cd starter
        ncu -u

    - name: Update Package Lock
      run: |
        cd starter
        npm i --package-lock-only

    - name: Deploy to external repository
      uses: cpina/github-action-push-to-another-repository@main
      env:
        API_TOKEN_GITHUB: ${{ secrets.GH_ACCESS_TOKEN }}
      with:
        source-directory: starter/
        destination-github-username: rpgjs
        destination-repository-name: starter
        user-email: "${{ github.actor}}@users.noreply.github.com"
        commit-message: update to latest RPGJS version
        target-branch: v4