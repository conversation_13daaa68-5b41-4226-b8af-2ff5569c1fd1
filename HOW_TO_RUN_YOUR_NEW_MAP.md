# 🎮 How to Run Your New RPG-JS Map

## ✅ What We've Accomplished

Your new map has been successfully created and integrated into the RPG-JS starter-kit! Here's what was added:

### 📁 Files Created:
1. **TMX Map File**: `packages/plugins/starter-kit/src/server/maps/tmx/newmap.tmx`
   - 30x30 tile map (960x960 pixels)
   - Walkable ground terrain
   - Decorative grass elements
   - Collision obstacles for testing
   - Player spawn point at (96, 96)

2. **Map Class**: `packages/plugins/starter-kit/src/server/maps/newmap.ts`
   - Proper `@MapData` decorator
   - Map ID: 'newmap'
   - Event hooks for player join/leave
   - Console logging for debugging

3. **Module Integration**: Updated `packages/plugins/starter-kit/src/server/index.ts`
   - Added NewMap import
   - Registered in maps array
   - Set as default spawn map

## 🚧 Current Issue: Node.js Version Compatibility

The project is having trouble with Node.js v23.9.0 and the `canvas` dependency. Here are solutions:

### Option 1: Use Node Version Manager (Recommended)

1. **Install nvm (if not already installed):**
   ```bash
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
   source ~/.zshrc
   ```

2. **Install and use Node.js v18 (LTS):**
   ```bash
   nvm install 18
   nvm use 18
   ```

3. **Try installing dependencies again:**
   ```bash
   cd /Users/<USER>/github/RPG-JS
   npm install
   ```

### Option 2: Use Docker (Alternative)

If you have Docker installed:

1. **Create a Dockerfile in the project root:**
   ```dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm install
   COPY . .
   EXPOSE 3000
   CMD ["npm", "run", "dev"]
   ```

2. **Build and run:**
   ```bash
   docker build -t rpgjs .
   docker run -p 3000:3000 rpgjs
   ```

### Option 3: Skip Canvas Dependency (Quick Test)

For a quick test without canvas:

1. **Remove canvas from package.json temporarily:**
   ```bash
   cd /Users/<USER>/github/RPG-JS
   npm uninstall canvas
   ```

2. **Install other dependencies:**
   ```bash
   npm install --ignore-scripts
   ```

## 🎯 How to Test Your New Map

Once dependencies are resolved:

### 1. Build the Project
```bash
cd /Users/<USER>/github/RPG-JS
npm run build
```

### 2. Run the Sample Project
```bash
cd packages/sample
npm run dev
```

### 3. Open Your Browser
Navigate to `http://localhost:3000` (or the port shown in terminal)

### 4. What You Should See
- Your character spawns on the new map
- You can walk around on the ground tiles
- You'll be blocked by the colored obstacle rectangles
- Console logs will show map loading and player events

## 🗺️ Map Features

Your new map includes:

- **Ground Layer**: Basic walkable terrain using BaseChip tileset
- **Decoration Layer**: Grass patches for visual appeal
- **Collision Objects**: 
  - Brown rectangle (320x320, 128x64)
  - Green square (640x480, 96x96)
  - Blue rectangle (160x640, 192x32)
- **Spawn Point**: Player starts at (96, 96)

## 🔧 Customizing Your Map

### Adding More Obstacles
Edit `packages/plugins/starter-kit/src/server/maps/tmx/newmap.tmx` and add more objects in the `<objectgroup>` section:

```xml
<object id="5" x="400" y="200" width="64" height="64">
  <properties>
    <property name="color" value="0xFF0000"/>
    <property name="obstacle" type="bool" value="true"/>
  </properties>
</object>
```

### Adding NPCs or Events
Edit `packages/plugins/starter-kit/src/server/maps/newmap.ts`:

```typescript
@MapData({
    id: 'newmap',
    file: require('./tmx/newmap.tmx'),
    name: 'New Adventure Map',
    events: [
        // Add your events here
    ]
})
```

### Changing Spawn Location
Modify the start point in the TMX file or use teleport in the map class:

```typescript
onJoin(player: RpgPlayer) {
    player.teleport({ x: 200, y: 200 })
}
```

## 🐛 Troubleshooting

### Map Not Loading
- Check console for errors
- Verify TMX file syntax
- Ensure tilesets exist in client assets

### Player Can't Move
- Check collision properties in TMX
- Verify player hitbox settings
- Look for JavaScript errors

### Textures Missing
- Ensure tileset images are in `packages/plugins/starter-kit/src/client/maps/assets/`
- Check tileset references in TMX file

## 🎉 Next Steps

Once your map is running:

1. **Add NPCs**: Create interactive characters
2. **Add Items**: Place collectible objects
3. **Create Teleports**: Connect to other maps
4. **Add Sounds**: Background music and effects
5. **Design Quests**: Interactive storylines

Your new map is ready to go! The main challenge now is resolving the Node.js/canvas compatibility issue.
