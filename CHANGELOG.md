# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [4.3.1](https://github.com/RSamaium/RPG-JS/compare/v4.3.0...v4.3.1) (2024-01-29)


### Bug Fixes

* lerna publish packages ([41c6ef0](https://github.com/RSamaium/RPG-JS/commit/41c6ef0e5b65d9084ecab40f4c65ef2d5d04a658))





# [4.3.0](https://github.com/RSamaium/RPG-JS/compare/v4.2.2...v4.3.0) (2024-01-29)


### Bug Fixes

* build spec ([65d48c0](https://github.com/RSamaium/RPG-JS/commit/65d48c04f8bdf53759205147ec80e00c96ed5fd9))
* server url env ([e6a1237](https://github.com/RSamaium/RPG-JS/commit/e6a1237e360f7a25e2e8194127473e8a74d0b0ab))
* unit tests compiler ([1292365](https://github.com/RSamaium/RPG-JS/commit/129236510198a08dbf376977068939b47d1ae350))
* unit tests compiler ([6277f1b](https://github.com/RSamaium/RPG-JS/commit/6277f1b6f201c1f06b5058a3c985e41952520b55))
* wallet address ([4f95fa5](https://github.com/RSamaium/RPG-JS/commit/4f95fa551f2dcc1cce23cc14a8dbbef677d25353))


### Features

* loading the game in a React app ([437917a](https://github.com/RSamaium/RPG-JS/commit/437917ace5de1a88777d84c4b39a48e147f77de4))
* reat app integration ([1b9b5d8](https://github.com/RSamaium/RPG-JS/commit/1b9b5d8bb5dd02bcf4a68ccd5eee19c6fc00a4eb))





## [4.2.2](https://github.com/RSamaium/RPG-JS/compare/v4.2.1...v4.2.2) (2024-01-15)

**Note:** Version bump only for package rpgjs





## [4.2.1](https://github.com/RSamaium/RPG-JS/compare/v4.2.0...v4.2.1) (2024-01-12)


### Bug Fixes

*  lag after action ([d057e17](https://github.com/RSamaium/RPG-JS/commit/d057e1736475e03a0553882a53b96a4d77e65acf))
* [#277](https://github.com/RSamaium/RPG-JS/issues/277) #community-256 ([924ec6d](https://github.com/RSamaium/RPG-JS/commit/924ec6d6636b51eeb46cb1509c7244f69a8adb49)), closes [#community-256](https://github.com/RSamaium/RPG-JS/issues/community-256)
* #community-261 ([8221085](https://github.com/RSamaium/RPG-JS/commit/8221085a962b1555aa20ba154b6790d4522da971)), closes [#community-261](https://github.com/RSamaium/RPG-JS/issues/community-261)
* simple-room exports ([9592d18](https://github.com/RSamaium/RPG-JS/commit/9592d186cdb7d66dba4fe7b49d31b2fc97686677))
* spritesheetDirectories warning ([4b34a15](https://github.com/RSamaium/RPG-JS/commit/4b34a15d60261501069100fb22aa0901b32a1d98))
* tiled parser throw error ([4adfaea](https://github.com/RSamaium/RPG-JS/commit/4adfaea557b386556b459d7f64b8a24a24116aa8))





# [4.2.0](https://github.com/RSamaium/RPG-JS/compare/v4.1.3...v4.2.0) (2023-12-09)


### Features

* inject function ([902e62f](https://github.com/RSamaium/RPG-JS/commit/902e62ff4fdd9b5bd26ee7d5be9ccae2b051f248))
* propagate event directive ([edbab50](https://github.com/RSamaium/RPG-JS/commit/edbab506a2552ff64d2f638d1e088748cbc8cc86))





## [4.1.3](https://github.com/RSamaium/RPG-JS/compare/v4.1.2...v4.1.3) (2023-11-17)

**Note:** Version bump only for package rpgjs





## [4.1.2](https://github.com/RSamaium/RPG-JS/compare/v4.1.1...v4.1.2) (2023-10-30)


### Bug Fixes

* **doc:** react gui ([ecfcc4d](https://github.com/RSamaium/RPG-JS/commit/ecfcc4d0ad2ecf3a5ef6f55fd7526ab9cd4b102c))
* **gui:** don't propagate events ([1439e0f](https://github.com/RSamaium/RPG-JS/commit/1439e0fa4718cbdc07cd2ceb8ff4067e116cef9b))





## [4.1.1](https://github.com/RSamaium/RPG-JS/compare/v4.1.0...v4.1.1) (2023-10-27)


### Bug Fixes

* **compiler:**  move images in RPG mode #community-216 ([182f2f1](https://github.com/RSamaium/RPG-JS/commit/182f2f11e53813cb7794295274b67d185906eb84)), closes [#community-216](https://github.com/RSamaium/RPG-JS/issues/community-216)
* **gui:** get vue instance #community-215 ([2874815](https://github.com/RSamaium/RPG-JS/commit/287481517028196f1bf8bc327b68134e8f5f63d0)), closes [#community-215](https://github.com/RSamaium/RPG-JS/issues/community-215)
* **scene:** click in scene map ([04d56d6](https://github.com/RSamaium/RPG-JS/commit/04d56d6e07c58e2c039732e35ae3b94fc6751fa5))
* **test:** clear after scene tests ([e9a934c](https://github.com/RSamaium/RPG-JS/commit/e9a934c7fc197079036628a94891c371bae4edb2))
* **tests:** size of ([558316e](https://github.com/RSamaium/RPG-JS/commit/558316e9c63282eef0ce6dd1594cc7b1a836c1d2))





# [4.1.0](https://github.com/RSamaium/RPG-JS/compare/v4.0.5...v4.1.0) (2023-10-20)


### Features

* onDetectInShape and onDetectOutShape hooks in event ([b2c6a2f](https://github.com/RSamaium/RPG-JS/commit/b2c6a2f98b3bcc992deb0473a9fb1699874b6e48))
* react ([4e02244](https://github.com/RSamaium/RPG-JS/commit/4e0224465fc8f76434039c464f063f929fb861fd))





## 4.0.5 (2023-10-18)


### Bug Fixes

* flickering motion of an event as it moves ([b2b8832](https://github.com/RSamaium/RPG-JS/commit/b2b8832a1582933afb64c698f40d1b0e72021780))





## 4.0.4 (2023-10-13)

**Note:** Version bump only for package rpgjs





## 4.0.3 (2023-10-10)

**Note:** Version bump only for package rpgjs





## 4.0.2 (2023-10-03)

**Note:** Version bump only for package rpgjs





## 4.0.1 (2023-10-03)

**Note:** Version bump only for package rpgjs





# 4.0.0-rc.13 (2023-09-09)

**Note:** Version bump only for package rpgjs





# 4.0.0-rc.12 (2023-09-08)

**Note:** Version bump only for package rpgjs





# 4.0.0-rc.11 (2023-08-30)

**Note:** Version bump only for package rpgjs





# 4.0.0-rc.10 (2023-08-28)

**Note:** Version bump only for package rpgjs





# 4.0.0-rc.9 (2023-08-25)

**Note:** Version bump only for package rpgjs





# 4.0.0-rc.8 (2023-08-23)

**Note:** Version bump only for package rpgjs





# [4.0.0-rc.6](https://github.com/RSamaium/RPG-JS/compare/v4.0.0-rc.5...v4.0.0-rc.6) (2023-08-20)

**Note:** Version bump only for package rpgjs





# [4.0.0-rc.5](https://github.com/RSamaium/RPG-JS/compare/v4.0.0-rc.4...v4.0.0-rc.5) (2023-08-16)

**Note:** Version bump only for package rpgjs
