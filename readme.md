![Header icon](/docs/header.png)

<p align="center">
  <img src="https://img.shields.io/npm/v/@rpgjs/server" alt="Version">
  <img src="https://img.shields.io/npm/dm/@rpgjs/server" alt="Downloads">
  <img src="https://img.shields.io/github/license/RSamaium/RPG-JS" alt="License">
  <img src="https://img.shields.io/github/commit-activity/m/RSamaium/RPG-JS" alt="Activity">
</p>

# RPG JS v4 : Create RPG and MMORPG in your browser

RPG JS is a versatile TypeScript framework meticulously designed to empower developers in the creation of both traditional Role-Playing Games (RPGs) and immersive Massively Multiplayer Online Role-Playing Games (MMORPGs). Our primary objective is to offer a seamless development experience where your codebase remains consistent, regardless of the game's nature.

With RPG JS, we aspire to provide developers with a robust foundation that adapts effortlessly to your creative vision. Whether you're crafting epic solo adventures or building vast multiplayer worlds, RPG JS ensures that your development journey remains cohesive and efficient. Our goal is to simplify the complexities of game development, allowing you to focus on bringing captivating stories and engaging gameplay to life.

## WebSite and Documentation

- Website: [https://rpgjs.dev](https://rpgjs.dev)
- Documentation: [https://docs.rpgjs.dev](https://docs.rpgjs.dev)
- Examples: [https://playground.rpgjs.dev](https://playground.rpgjs.dev)
- Community: [https://community.rpgjs.dev](https://community.rpgjs.dev)

## Demo 

[Demo](https://demo.rpgjs.dev)

![Demo](/docs/demo.png)

## Resources

- Use LPC (Liberated Pixel Cup) Spritesheets: https://github.com/dominx99/rpgjs-tutorial#lpc-liberated-pixel-cup

## Getting Started

```bash
npx degit rpgjs/starter my-rpg-game
cd my-rpg-game
npm install
npm run dev
```

Then go to port localhost:3000

## Features

| Feature                        | Description                                                                                         |
|--------------------------------|-----------------------------------------------------------------------------------------------------|
| 🌟 Create Events                | Build NPCs, monsters, and more to shape your game's storyline.                                      |
| 🌎 Tiled Map Editor             | Craft intricate game worlds with multiple layers, shapes, and collisions.                         |
| 🎨 Customize Your Hero          | Personalize your hero's appearance, animations, and attributes.                                      |
| 🖥️ WebGL Rendering              | Enjoy high-performance game rendering with WebGL and PixiJS.                                        |
| ⚙️ MMORPG Performance           | Optimize for multiple players with zone-based processing and client-side prediction.              |
| 🚀 Designed for Scaling         | Utilize Agones and Kubernetes to scale your game for thousands of players.                         |
| 📱 Cross-Platform Gaming        | Play on mobile, gamepad, or keyboard for versatile gaming experiences.                               |
| 🌐 Using Vue.js for UI           | Leverage Vue.js for user interfaces, including dialog boxes and menus.                                |
| 💻 Code with TypeScript         | Develop with TypeScript for enhanced code structure and clarity.                                    |
| 📜 RPG-Focused API              | Simplify RPG development with an intuitive API for in-game actions.                                  |
| 🔄 Same Code: MMORPG or RPG     | Create both MMORPGs and traditional RPGs using the same codebase.                                    |
| 🧩 Modular                      | Expand functionality with ease through a modular design.                                            |
| 🧪 Unit Tests                   | Ensure game stability and reliability with built-in unit testing.                                    |
| 📦 And Other Plugins            | Enhance your game with additional features like chat, title screens, and more.                      |

## Contribute to developments

To contribute to the developments, install the sources locally:

```bash
git clone https://github.com/RSamaium/RPG-JS.git
npm install
npm run dev
```

The game can be found in `@rpgjs/sample2` package.

### For documentation

```sh
cd docs 
npm install
npm run dev
```

## License

MIT. Free for commercial use.