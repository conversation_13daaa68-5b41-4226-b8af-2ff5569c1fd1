{"name": "rpgjs", "version": "4.0.0", "engines": {"node": ">=11.0.0"}, "description": "RPG JS is a framework for creating RPGs and MMORPG", "dependencies": {"canvas": "^2.11.2", "concurrently": "^7.6.0", "tsc-esm-fix": "^2.20.14", "vue": "^3.2.47"}, "devDependencies": {"axios": "^1.5.0", "git-parse": "^3.0.1", "jsdom": "^21.1.1", "lerna": "^6.6.1", "typescript": "^5.0.2", "vitest": "^1.2.2"}, "scripts": {"postinstall": "lerna bootstrap --ci && npm run build:compiler && npm run build:types && npm run build:tiled && npm run build:common && npm run build:client && npm run build:database && npm run build:server && npm run build:testing  && cd packages/client && cd ../../packages/plugins/agones/matchmaker && npm i", "test": "RPGJS_TEST=1 vitest --config ./packages/compiler/src/test/vitest.config.ts && cd ./packages/compiler && npm test", "lerna:publish": "lerna publish --ignore-prepublish --force-publish --no-private --no-push --no-git-tag-version", "lerna:actions:publish": "lerna publish from-git --ignore-prepublish --force-publish --no-private --no-push --no-git-tag-version --yes", "lerna:ls": "lerna ls", "build": "concurrently -n build \"npm:build:*\" && npm run fix-esm", "build:standalone": "cd packages/standalone && npm run build:browser", "build:defaultGui": "cd packages/plugins/default-gui && npm run build:browser", "build:common": "cd packages/common && npm run build", "build:server": "cd packages/server && npm run build", "build:client": "cd packages/client && npm run build", "build:database": "cd packages/database && npm run build", "build:testing": "cd packages/testing && npm run build", "build:tiled": "cd packages/tiled && npm run build", "build:types": "cd packages/types && npm run build", "build:compiler": "cd packages/compiler && npm run build", "build:agones-mm": "cd packages/plugins/agones/matchmaker && npm run build", "watch:common": "cd packages/common && npm run watch", "watch:client": "cd packages/client && npm run watch", "watch:server": "cd packages/server && npm run watch", "watch:testing": "cd packages/testing && npm run watch", "watch:database": "cd packages/database && npm run watch", "watch:tiled": "cd packages/tiled && npm run watch", "watch:types": "cd packages/types && npm run watch", "watch:compiler": "cd packages/compiler && npm run watch", "watch:agones-mm": "cd packages/plugins/agones/matchmaker && npm run watch", "dev:watch": "concurrently -n rpgjs \"npm:watch:*\"", "dev": "concurrently -n rpgjs,sample \"npm:watch:*\" \"cd packages/sample && npm run dev\"", "dev:doc": "cd docs && npm run dev", "doc:build": "cd docs && npm run build", "fix-esm": "tsc-esm-fix  --target='packages/*/lib/**/*.js' --dirnameVar=false && tsc-esm-fix  --target='packages/plugins/agones/matchmaker/lib/*.js'"}, "repository": {"type": "git", "url": ""}, "keywords": ["rpg", "mmorpg", "framework"], "author": "<PERSON>", "license": "MIT", "type": "module"}