config:
  target: "http://localhost:3000"
  phases:
    - duration: 50
      arrivalRate: 3
  engines:
   socketio-v3: {}
  processor: "./random-move.js"
scenarios:
  - name: SocketIO
    engine: "socketio-v3"
    flow: 
       - loop:
          - function: "randomDir"
          - emit: 
              channel: move
              data: { input: "{{direction}}", options: {"movement": true}, step: 0 }
          - think: 1
       - think: 50