{
    "compilerOptions": {
        "target": "es2020",
        "baseUrl": "./",
        "types": ["node"],
        "module": "commonjs",
        "outDir": "./lib",
        "rootDir": "./tests/unit-tests",
        "strict": true,
        "sourceMap": true,
        "strictNullChecks": true,
        "strictPropertyInitialization": false,
        "moduleResolution": "node",
        "esModuleInterop": true,
        "experimentalDecorators": true,
        "emitDecoratorMetadata": true,
        "removeComments": false,
        "noUnusedParameters": false,
        "noUnusedLocals": false,
        "noImplicitThis": false,
        "noImplicitAny": false,
        "noImplicitReturns": false,
        "declaration": true,
        "paths": {
            "server!*": ["*"],
            "client!*": ["*"],
            "rpg!*": ["*"],
            "mmorpg!*": ["*"],
            "development!*": ["*"],
            "production!*": ["*"],
            "@rpgjs/*": ["./packages/*"]
         }
    },
    "include": [
        "tests/**/*.ts",
        "specs/plugins/*.ts",
        "index.d.ts",
        "packages/compiler/types/*.d.ts",
    ] 
}