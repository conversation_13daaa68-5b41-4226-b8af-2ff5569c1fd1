{"name": "@rpgjs/documentation", "version": "1.0.0", "description": "Documentation for RPG JS v3", "main": "index.js", "scripts": {"dev": "vitepress dev .", "build": "npm run generate && vitepress build .", "generate": "node extract"}, "keywords": [], "author": "", "license": "MIT", "dependencies": {"@vue/theme": "^2.2.4", "comment-parser": "^1.4.0", "compare-versions": "^6.1.0"}, "devDependencies": {"vitepress": "^1.0.0-rc.4"}}